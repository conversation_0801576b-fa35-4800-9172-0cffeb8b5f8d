# Password Requirements Implementation Guide

This document provides a comprehensive guide for implementing secure password fields with validation, visibility toggle, and internationalization using HTMX and Bootstrap.

## Password Requirements Specification

### Security Requirements
- **Minimum Length**: 8 characters
- **Uppercase Letter**: At least one (A-Z)
- **Lowercase Letter**: At least one (a-z)
- **Number**: At least one digit (0-9)
- **Special Character**: At least one from the set: `@#$!%*?&.;:<>`
- **Maximum Length**: No explicit limit (browser default)

### Regex Pattern
```regex
^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$!%*?&.;:<>])[A-Za-z\d@#$!%*?&.;:<>]{8,}$
```

## HTML Implementation

### Basic Password Field Structure
```html
<!-- Password Field with Toggle -->
<div class="mb-3">
    <label for="password" class="form-label">
        <span th:text="#{field.password}">Password</span> *
    </label>
    <div class="input-group">
        <input type="password"
               class="form-control"
               id="password"
               name="password"
               minlength="8"
               pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$!%*?&.;:<>])[A-Za-z\d@#$!%*?&.;:<>]{8,}"
               th:title="#{password.validation.pattern}"
               required>
        <button class="btn btn-outline-secondary"
                type="button"
                id="togglePassword"
                onclick="togglePasswordVisibility('password', 'togglePassword')"
                title="Show/Hide Password">
            <i class="ti ti-eye" id="togglePasswordIcon"></i>
        </button>
    </div>
    <div class="invalid-feedback" th:text="#{password.validation.message}"></div>
    <div class="form-text">
        <small th:text="#{password.requirements}">Password requirements text</small>
    </div>
</div>
```

### Key HTML Attributes
- `type="password"` - Hides input by default
- `minlength="8"` - HTML5 minimum length validation
- `pattern="..."` - HTML5 regex validation
- `required` - Makes field mandatory
- `th:title` - Tooltip with validation message
- `th:field="*{password}"` - Thymeleaf binding (if using form objects)

## CSS Styling

### Password Toggle Button Styling
```css
/* Password toggle button styling */
.input-group .btn-outline-secondary {
    border-color: #d0d7de;
    color: #656d76;
    background-color: transparent;
}

.input-group .btn-outline-secondary:hover {
    background-color: #f6f8fa;
    border-color: #d0d7de;
    color: #24292f;
}

.input-group .btn-outline-secondary:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    border-color: #86b7fe;
}

.input-group .btn-outline-secondary i {
    font-size: 1rem;
    line-height: 1;
}

/* Validation styling for password field with toggle */
.input-group .form-control.is-invalid {
    border-right: 1px solid #ea5455;
}

.input-group .form-control.is-invalid + .btn {
    border-color: #ea5455;
}
```

### Form Validation Styling
```css
/* Form validation styling */
.invalid-feedback {
    display: block !important;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #ea5455 !important;
    font-weight: 500;
}

.form-control.is-invalid {
    border-color: #ea5455 !important;
    box-shadow: 0 0 0 0.2rem rgba(234, 84, 85, 0.25) !important;
}
```

## JavaScript Implementation

### Password Visibility Toggle Function
```javascript
// Password visibility toggle function
function togglePasswordVisibility(passwordFieldId, toggleButtonId) {
    const passwordField = document.getElementById(passwordFieldId);
    const toggleButton = document.getElementById(toggleButtonId);
    const toggleIcon = toggleButton.querySelector('i');
    
    if (passwordField && toggleIcon) {
        if (passwordField.type === 'password') {
            // Show password
            passwordField.type = 'text';
            toggleIcon.className = 'ti ti-eye-off';
            toggleButton.title = 'Hide Password';
        } else {
            // Hide password
            passwordField.type = 'password';
            toggleIcon.className = 'ti ti-eye';
            toggleButton.title = 'Show Password';
        }
    }
}

// Make function globally available
window.togglePasswordVisibility = togglePasswordVisibility;
```

### Form Validation Integration
```javascript
// Initialize form validation for password fields
function initializePasswordValidation() {
    const passwordFields = document.querySelectorAll('input[type="password"]');
    
    passwordFields.forEach(field => {
        field.addEventListener('input', function() {
            validatePasswordField(this);
        });
        
        field.addEventListener('blur', function() {
            validatePasswordField(this);
        });
    });
}

// Validate individual password field
function validatePasswordField(field) {
    const pattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$!%*?&.;:<>])[A-Za-z\d@#$!%*?&.;:<>]{8,}$/;
    const isValid = pattern.test(field.value);
    
    if (field.value && !isValid) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
    } else if (field.value && isValid) {
        field.classList.add('is-valid');
        field.classList.remove('is-invalid');
    } else {
        field.classList.remove('is-invalid', 'is-valid');
    }
}
```

## Internationalization (i18n)

### Message Bundle Keys
Add these keys to your `messages.properties` files:

```properties
# Password Field Labels
field.password=Password
field.password.confirm=Confirm Password

# Password Validation Messages
password.validation.pattern=Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character (@#$!%*?&.;:<>)
password.validation.message=Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character
password.requirements=Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.

# Password Field Validation
password.field.required=Password is required
password.field.minlength=Password must be at least 8 characters long
password.field.pattern=Password does not meet security requirements

# Password Confirmation
password.confirm.required=Password confirmation is required
password.confirm.mismatch=Passwords do not match
```

### Multi-language Support
Create corresponding files for other languages:
- `messages_en_US.properties`
- `messages_es_ES.properties`
- `messages_fr_FR.properties`
- etc.

## HTMX Integration

### Form Submission with HTMX
```html
<form hx-post="/api/users/create"
      hx-swap="innerHTML"
      hx-target="#response"
      hx-trigger="bs-send"
      class="needs-validation"
      novalidate>
    
    <!-- Password field implementation here -->
    
    <button type="submit" class="btn btn-primary">
        Submit
    </button>
</form>
```

### Server Response Handling
```javascript
// Handle HTMX form responses
document.body.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.pathInfo.requestPath.includes('/create') || 
        event.detail.pathInfo.requestPath.includes('/update')) {
        
        const form = event.detail.elt;
        if (form && form.classList.contains('needs-validation')) {
            form.classList.remove('was-validated');
        }
        
        handleFormResponse(event);
    }
});
```

## Server-Side Validation

### Bean Validation Annotations
```java
public class UserDto {
    @NotBlank(message = "{password.field.required}")
    @Size(min = 8, message = "{password.field.minlength}")
    @Pattern(
        regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@#$!%*?&.;:<>])[A-Za-z\\d@#$!%*?&.;:<>]{8,}$",
        message = "{password.field.pattern}"
    )
    private String password;
    
    // getters and setters
}
```

### Controller Validation
```java
@PostMapping("/create")
public ResponseEntity<?> createUser(@Valid @RequestBody UserDto userDto, 
                                   BindingResult bindingResult,
                                   Locale locale) {
    if (bindingResult.hasErrors()) {
        Map<String, String> fieldErrors = new HashMap<>();
        bindingResult.getFieldErrors().forEach(error -> 
            fieldErrors.put(error.getField(), 
                messageSource.getMessage(error, locale))
        );
        
        return ResponseEntity.badRequest().body(Map.of("fieldErrors", fieldErrors));
    }
    
    // Process valid user creation
    return ResponseEntity.ok(Map.of(
        "title", messageSource.getMessage("user.created.title", null, locale),
        "text", messageSource.getMessage("user.created.message", null, locale),
        "icon", "success"
    ));
}
```

## Usage Examples

### Basic Password Field
```html
<!-- Minimal implementation -->
<div class="mb-3">
    <label for="password" class="form-label">Password *</label>
    <div class="input-group">
        <input type="password" class="form-control" id="password" name="password" 
               minlength="8" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$!%*?&.;:<>])[A-Za-z\d@#$!%*?&.;:<>]{8,}" required>
        <button class="btn btn-outline-secondary" type="button" 
                onclick="togglePasswordVisibility('password', this)">
            <i class="ti ti-eye"></i>
        </button>
    </div>
    <div class="invalid-feedback">Password does not meet requirements</div>
</div>
```

### Password Confirmation Field
```html
<!-- Password confirmation with matching validation -->
<div class="mb-3">
    <label for="confirmPassword" class="form-label">Confirm Password *</label>
    <div class="input-group">
        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" 
               required data-match="password">
        <button class="btn btn-outline-secondary" type="button" 
                onclick="togglePasswordVisibility('confirmPassword', this)">
            <i class="ti ti-eye"></i>
        </button>
    </div>
    <div class="invalid-feedback">Passwords do not match</div>
</div>
```

## Best Practices

### Security Considerations
1. **Always validate on server-side** - Client-side validation is for UX only
2. **Use HTTPS** - Never transmit passwords over unencrypted connections
3. **Hash passwords** - Use bcrypt or similar strong hashing algorithms
4. **Implement rate limiting** - Prevent brute force attacks
5. **Consider password history** - Prevent reuse of recent passwords

### Accessibility
1. **Proper labeling** - Use `<label>` elements with `for` attributes
2. **ARIA attributes** - Add `aria-describedby` for password requirements
3. **Keyboard navigation** - Ensure toggle button is keyboard accessible
4. **Screen reader support** - Provide clear feedback for validation states

### User Experience
1. **Real-time validation** - Show validation feedback as user types
2. **Clear requirements** - Display password requirements prominently
3. **Visual feedback** - Use colors and icons to indicate validation state
4. **Progressive enhancement** - Ensure functionality works without JavaScript

## Testing Checklist

- [ ] Password field accepts valid passwords
- [ ] Password field rejects invalid passwords
- [ ] Toggle button shows/hides password correctly
- [ ] Validation messages appear in correct language
- [ ] Form submission works with HTMX
- [ ] Server-side validation catches invalid passwords
- [ ] CSS styling displays correctly
- [ ] Accessibility features work properly
- [ ] Mobile responsiveness is maintained
- [ ] Password confirmation matching works (if applicable)

This guide provides a complete implementation for secure, user-friendly password fields that integrate seamlessly with HTMX-based applications while maintaining high security standards and excellent user experience.
