<!DOCTYPE html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/web/thymeleaf/layout"
      layout:decorate="~{layout}">

<head>
    <title th:text="#{error.404.title}">Page Not Found - 404</title>
</head>

<body>

<div layout:fragment="content">
    <div class="col-12">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <!-- Error Icon -->
                        <div class="mb-4">
                            <i class="ti ti-error-404 text-warning" style="font-size: 4rem;"></i>
                        </div>

                        <!-- Error Title -->
                        <h1 class="display-4 text-warning mb-3" th:text="#{error.404.heading}">404</h1>
                        <h2 class="h4 mb-3" th:text="#{error.404.subheading}">Application Not Found</h2>

                        <!-- Error Message -->
                        <div class="alert alert-warning" role="alert">
                            <h5 class="alert-heading">
                                <i class="ti ti-search me-2"></i>
                                <span th:text="#{error.404.app.not.available}">Application Not Available</span>
                            </h5>
                            <p class="mb-0" th:text="#{error.404.message}">
                                The application you're looking for doesn't exist or is not available.
                                This could be because:
                            </p>
                            <ul class="list-unstyled mt-3 mb-0 text-start">
                                <li><i class="ti ti-point me-2"></i><span th:text="#{error.404.reason.misspelled}">The application name is misspelled in the URL</span></li>
                                <li><i class="ti ti-point me-2"></i><span th:text="#{error.404.reason.removed}">The application has been removed or disabled</span></li>
                                <li><i class="ti ti-point me-2"></i><span th:text="#{error.404.reason.broken.link}">You followed an outdated or broken link</span></li>
                                <li><i class="ti ti-point me-2"></i><span th:text="#{error.404.reason.maintenance}">The application is under maintenance</span></li>
                            </ul>
                        </div>

                        <!-- Available Apps -->
                        <div th:if="${userAvailableApps != null and userAvailableApps.size() > 0}"
                             class="card bg-light border-0 mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ti ti-apps me-2"></i>
                                    <span th:text="#{error.404.available.apps}">Your Available Applications</span>
                                </h6>
                                <div class="d-flex flex-wrap gap-2 justify-content-center">
                                    <a th:each="app : ${userAvailableApps}"
                                       th:href="@{'/app/' + ${app.appName.toLowerCase()}}"
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="ti ti-external-link me-1"></i>
                                        <span th:text="#{${'app.name.' + app.appName.toLowerCase()}}">App Name</span>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex flex-column flex-sm-row gap-2 justify-content-center">
                            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left me-2"></i>
                                Go Back
                            </a>
                            <a th:href="@{/dashboard}" class="btn btn-primary">
                                <i class="ti ti-home me-2"></i>
                                Return to Dashboard
                            </a>
                        </div>

                        <!-- User Information (if available) -->
                        <div th:if="${currentUser}" class="mt-4 pt-3 border-top">
                            <small class="text-muted">
                                Logged in as: <strong th:text="${currentUser.fullName}">User Name</strong>
                                (<span th:text="${currentUser.email}"><EMAIL></span>)
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
