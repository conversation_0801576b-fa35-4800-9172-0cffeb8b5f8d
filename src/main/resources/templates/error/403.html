<!DOCTYPE html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/web/thymeleaf/layout"
      layout:decorate="~{layout}">

<head>
    <title th:text="#{error.403.title}">Access Denied - 403</title>
</head>

<body>

<div layout:fragment="content">
    <div class="col-12">
        <div class="row justify-content-center">
            <div class="col-md-10 col-lg-8">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <!-- Error Icon -->
                        <div class="mb-4">
                            <i class="ti ti-shield-x text-danger" style="font-size: 4rem;"></i>
                        </div>

                        <!-- Error Title -->
                        <h1 class="display-4 text-danger mb-3" th:text="#{error.403.heading}">403</h1>
                        <h2 class="h4 mb-3" th:text="#{error.403.subheading}">Access Denied</h2>

                        <!-- Detailed Error Message (if available) -->
                        <div th:if="${accessDeniedException}" class="alert alert-danger" role="alert">
                            <h5 class="alert-heading">
                                <i class="ti ti-exclamation-triangle me-2"></i>
                                <span th:text="${accessDeniedException.userFriendlyMessage}">Access Denied</span>
                            </h5>
                            <hr>
                            <p class="mb-3" th:text="${accessDeniedException.detailedExplanation ?: #{error.403.detailed.explanation.fallback}}">
                                Detailed explanation of why access was denied.
                            </p>

                            <!-- Role Information (for role-based denials) -->
                            <div th:if="${accessDeniedException.reason.name() == 'INSUFFICIENT_ROLE'}" class="row text-start">
                                <div class="col-md-6">
                                    <div class="card bg-light border-warning">
                                        <div class="card-body">
                                            <h6 class="card-title text-warning">
                                                <i class="ti ti-user me-2"></i>
                                                <span th:text="#{error.403.role.current}">Your Current Role</span>
                                            </h6>
                                            <p class="card-text mb-0">
                                                <span class="badge bg-secondary" th:text="${accessDeniedException.userRole?.name()?.replace('ROLE_', '')}">USER</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light border-danger">
                                        <div class="card-body">
                                            <h6 class="card-title text-danger">
                                                <i class="ti ti-shield me-2"></i>
                                                <span th:text="#{error.403.role.required}">Required Role</span>
                                            </h6>
                                            <p class="card-text mb-0">
                                                <span class="badge bg-danger" th:text="${accessDeniedException.requiredRole?.name()?.replace('ROLE_', '')}">ADMIN</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Application Information -->
                            <div th:if="${accessDeniedException.appName}" class="mt-3">
                                <div class="card bg-light border-info">
                                    <div class="card-body">
                                        <h6 class="card-title text-info">
                                            <i class="ti ti-apps me-2"></i>
                                            <span th:text="#{error.403.app.requested}">Requested Application</span>
                                        </h6>
                                        <p class="card-text mb-0">
                                            <strong th:text="${#strings.capitalize(accessDeniedException.appName)}">Application Name</strong>
                                            <span th:if="${requestedOperation}" class="text-muted">
                                                - <span th:text="${requestedOperation}">Operation</span>
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Generic Error Message (fallback) -->
                        <div th:unless="${accessDeniedException}" class="alert alert-danger" role="alert">
                            <h5 class="alert-heading">
                                <i class="ti ti-exclamation-triangle me-2"></i>
                                <span th:text="#{error.403.insufficient.permissions}">Insufficient Permissions</span>
                            </h5>
                            <p class="mb-0" th:text="#{error.403.generic.message}">
                                You don't have permission to access this application or resource.
                                This could be because:
                            </p>
                            <ul class="list-unstyled mt-3 mb-0 text-start">
                                <li><i class="ti ti-point me-2"></i><span th:text="#{error.403.reason.company.no.access}">Your company doesn't have access to this application</span></li>
                                <li><i class="ti ti-point me-2"></i><span th:text="#{error.403.reason.user.no.access}">You haven't been granted access to this application</span></li>
                                <li><i class="ti ti-point me-2"></i><span th:text="#{error.403.reason.insufficient.role}">Your role level is insufficient for this operation</span></li>
                                <li><i class="ti ti-point me-2"></i><span th:text="#{error.403.reason.account.disabled}">Your account may be disabled or locked</span></li>
                            </ul>
                        </div>

                        <!-- Suggested Action -->
                        <div th:if="${accessDeniedException}" class="card bg-primary text-white mb-4">
                            <div class="card-body">
                                <h6 class="card-title text-white">
                                    <i class="ti ti-lightbulb me-2"></i>
                                    <span th:text="#{error.403.action.title}">What You Can Do</span>
                                </h6>
                                <p class="card-text mb-0" th:text="${accessDeniedException.suggestedAction ?: #{error.403.contact.admin.fallback}}">
                                    Contact your administrator for assistance
                                </p>
                            </div>
                        </div>

                        <!-- Help Information -->
                        <div class="card bg-light border-0 mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ti ti-info-circle me-2"></i>
                                    <span th:text="#{error.403.help.title}">Need Help?</span>
                                </h6>
                                <p class="card-text small mb-0">
                                    <span th:text="#{error.403.help.message}">Contact your system administrator or company admin to request access.</span>
                                    <span th:if="${requestedApp}" th:text="#{error.403.help.mention.app.fallback(${#strings.capitalize(requestedApp)})}">
                                        Make sure to mention that you need access to the Application application.
                                    </span>
                                </p>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex flex-column flex-sm-row gap-2 justify-content-center">
                            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left me-2"></i>
                                <span th:text="#{error.403.button.go.back}">Go Back</span>
                            </a>
                            <a th:href="@{/dashboard}" class="btn btn-primary">
                                <i class="ti ti-home me-2"></i>
                                <span th:text="#{error.403.button.return.dashboard}">Return to Dashboard</span>
                            </a>
                        </div>

                        <!-- User Information (if available) -->
                        <div th:if="${currentUser}" class="mt-4 pt-3 border-top">
                            <small class="text-muted" th:text="#{error.403.user.logged.in(${currentUser.fullName}, ${currentUser.email})}">
                                Logged in as: User Name (<EMAIL>)
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
