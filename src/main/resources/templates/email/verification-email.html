<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}">
<head>
    <meta charset="UTF-8">
    <title>[[#{email.verification.title}]]</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { width: 80%; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .button { display: inline-block; padding: 10px 20px; background-color: #7367f0; color: #ffffff !important; text-decoration: none; border-radius: 3px; cursor: pointer; }
        .footer { margin-top: 20px; font-size: 0.9em; color: #777; }
    </style>
</head>
<body>
    <div class="container">
        <h2>[[#{email.verification.title}]]</h2>
        <p>[[#{email.verification.greeting}]] <span th:text="${userName}">User</span>,</p>
        <p>[[#{email.verification.p.01}]]</p>
        <p>
            <a th:href="${verificationUrl}" class="button">[[#{email.verification.button}]]</a>
        </p>
        <p>[[#{email.verification.p.02}]]</p>
        <p>[[#{email.verification.p.03}]]</p>
        <p class="footer">
            [[#{email.verification.footer.01}]]<br/>
            [[#{email.verification.footer.02}]]
        </p>
    </div>
</body>
</html>
