.layout-navbar .navbar-dropdown.dropdown-notifications .dropdown-notifications-list .dropdown-notifications-item.marked-as-read .dropdown-notifications-read span {
    background-color: transparent !important;
}

/* ===== VUEXY ACTIVE MENU STATE STYLING ===== */


/* Active menu item styling - matches <PERSON><PERSON>xy template exactly */
.menu-item.active {
    background: linear-gradient(72deg, var(--bs-primary) 0%, rgba(var(--bs-primary-rgb), 0.7) 100%);
    box-shadow: 0 2px 6px 0 rgba(var(--bs-primary-rgb), 0.48);
    border-radius: 0.375rem;
    margin: 0.125rem 0.75rem;
    transform: translateY(-1px);
}

/* Active menu link styling */
.menu-item.active .menu-link {
    color: #fff !important;
    background: transparent;
}

/* Active menu icon styling */
.menu-item.active .menu-icon {
    color: #fff !important;
}

/* Active menu text styling */
.menu-item.active .menu-text,
.menu-item.active .menu-link > div {
    color: #fff !important;
    font-weight: 500;
}

/* Hover effect for active items */
.menu-item.active:hover {
    background: linear-gradient(72deg, var(--bs-primary) 0%, rgba(var(--bs-primary-rgb), 0.8) 100%);
    box-shadow: 0 4px 8px 0 rgba(var(--bs-primary-rgb), 0.56);
    transform: translateY(-2px);
}

/* Non-active menu items hover */
.menu-item:not(.active):hover {
    background-color: rgba(var(--bs-body-color-rgb), 0.04);
    border-radius: 0.375rem;
    margin: 0.125rem 0.75rem;
}

/* Smooth transitions for menu items */
.menu-item {
    transition: all 0.25s ease;
    margin: 0.125rem 0.75rem;
    border-radius: 0.375rem;
}

.menu-link {
    transition: all 0.25s ease;
    border-radius: 0.375rem;
}

/* Enhanced focus states for accessibility */
.menu-link:focus {
    outline: 2px solid rgba(var(--bs-primary-rgb), 0.5);
    outline-offset: 2px;
    border-radius: 0.375rem;
}

/* Menu text styling */
.menu-text {
    font-weight: 400;
    color: var(--bs-body-color);
}

/* Dark mode support for active states */
[data-bs-theme="dark"] .menu-item.active {
    background: linear-gradient(72deg, var(--bs-primary) 0%, rgba(var(--bs-primary-rgb), 0.8) 100%);
    box-shadow: 0 2px 6px 0 rgba(var(--bs-primary-rgb), 0.6);
}

[data-bs-theme="dark"] .menu-item:not(.active):hover {
    background-color: rgba(255, 255, 255, 0.04);
}

/* ===== VUEXY MENU ENHANCEMENTS ===== */

/* Menu item spacing and layout */
.menu-item {
    position: relative;
    margin-bottom: 0.125rem;
}

/* Menu link padding and layout */
.menu-item .menu-link {
    padding: 0.625rem 1.5rem;
    display: flex;
    align-items: center;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

/* Menu icon styling */
.menu-item .menu-icon {
    margin-right: 0.75rem;
    font-size: 1.125rem;
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

/* Menu text styling */
.menu-item .menu-text {
    flex: 1;
    font-size: 0.9375rem;
    line-height: 1.53;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Active state animation */
.menu-item.active {
    animation: menuActiveSlide 0.3s ease-out;
}

@keyframes menuActiveSlide {
    0% {
        transform: translateX(-10px) translateY(-1px);
        opacity: 0.8;
    }
    100% {
        transform: translateX(0) translateY(-1px);
        opacity: 1;
    }
}

/* Ripple effect for menu items */
.menu-item .menu-link::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
}

.menu-item:not(.active) .menu-link:hover::before {
    width: 100%;
    height: 100%;
}

/* Menu header styling enhancement */
.menu-header {
    padding: 1rem 1.5rem 0.5rem;
    margin-top: 1rem;
}

.menu-header:first-child {
    margin-top: 0;
}

.menu-header-text {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.4px;
    color: var(--bs-gray-500);
    line-height: 1;
}

/* Responsive adjustments */
@media (max-width: 1199.98px) {
    .menu-item .menu-link {
        padding: 0.5rem 1rem;
    }

    .menu-item .menu-icon {
        margin-right: 0.5rem;
    }
}