package ag.fuel.jobify.app.service;

import ag.fuel.jobify.app.entity.App;
import ag.fuel.jobify.app.repository.AppRepository;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing App entities.
 */
@Service
@RequiredArgsConstructor
public class AppService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppService.class);

    private final AppRepository appRepository;

    /**
     * Initialize the default apps (Purchasing, Finance, Budgets) if they don't exist.
     */
    @PostConstruct
    public void initDefaultApps() {
        LOGGER.info("Initializing default apps");

        // Check if Purchasing app exists, if not create it
        if (!appRepository.existsByAppName("Purchasing")) {
            App purchasingApp = new App("Purchasing", "Purchasing application for managing procurement processes");
            appRepository.save(purchasingApp);
            LOGGER.info("Created Purchasing app");
        }

        // Check if Finance app exists, if not create it
        if (!appRepository.existsByAppName("Finance")) {
            App financeApp = new App("Finance", "Finance application for managing financial processes");
            appRepository.save(financeApp);
            LOGGER.info("Created Finance app");
        }

        // Check if Budgets app exists, if not create it
        if (!appRepository.existsByAppName("Budgets")) {
            App budgetsApp = new App("Budgets", "Budgets application for managing budget processes");
            appRepository.save(budgetsApp);
            LOGGER.info("Created Budgets app");
        }
    }

    /**
     * Get all apps.
     *
     * @return a list of all apps
     */
    @Transactional(readOnly = true)
    public List<App> getAllApps() {
        return appRepository.findAll().stream()
                .filter(app -> app != null && app.getAppName() != null && !app.getAppName().isEmpty())
                .toList();
    }

    /**
     * Get an app by its ID.
     *
     * @param id the app ID
     * @return an Optional containing the app if found, or empty if not found
     */
    @Transactional(readOnly = true)
    public Optional<App> getAppById(Long id) {
        Optional<App> appOpt = appRepository.findById(id);
        // Filter out apps with null or empty appName
        if (appOpt.isPresent() && (appOpt.get().getAppName() == null || appOpt.get().getAppName().isEmpty())) {
            return Optional.empty();
        }
        return appOpt;
    }

    /**
     * Get an app by its name.
     *
     * @param appName the app name
     * @return an Optional containing the app if found, or empty if not found
     */
    @Transactional(readOnly = true)
    public Optional<App> getAppByName(String appName) {
        if (appName == null || appName.isEmpty()) {
            return Optional.empty();
        }
        Optional<App> appOpt = appRepository.findByAppName(appName);
        // Filter out apps with null or empty appName (should not happen if found by name, but just to be safe)
        if (appOpt.isPresent() && (appOpt.get().getAppName() == null || appOpt.get().getAppName().isEmpty())) {
            return Optional.empty();
        }
        return appOpt;
    }

    /**
     * Create a new app.
     *
     * @param app the app to create
     * @return the created app
     */
    @Transactional
    public App createApp(App app) {
        return appRepository.save(app);
    }

    /**
     * Update an existing app.
     *
     * @param app the app to update
     * @return the updated app
     */
    @Transactional
    public App updateApp(App app) {
        return appRepository.save(app);
    }

    /**
     * Delete an app by its ID.
     *
     * @param id the app ID
     */
    @Transactional
    public void deleteApp(Long id) {
        appRepository.deleteById(id);
    }

    /**
     * Check if an app with the given name exists.
     *
     * @param appName the app name
     * @return true if an app with the given name exists, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean existsByAppName(String appName) {
        return appRepository.existsByAppName(appName);
    }
}
