package ag.fuel.jobify.app.service;

import ag.fuel.jobify.app.entity.App;
import ag.fuel.jobify.app.entity.UserApp;
import ag.fuel.jobify.app.repository.UserAppRepository;
import ag.fuel.jobify.auth.entity.ERole;
import ag.fuel.jobify.company.entity.Company;
import ag.fuel.jobify.company.service.CompanyService;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.service.UserService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for managing UserApp entities.
 */
@Service
@RequiredArgsConstructor
public class UserAppService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserAppService.class);

    private final UserAppRepository userAppRepository;
    private final AppService appService;
    private final UserService userService;
    private final CompanyAppService companyAppService;
    private final CompanyService companyService;

    /**
     * Get all UserApp entities.
     *
     * @return a list of all UserApp entities
     */
    @Transactional(readOnly = true)
    public List<UserApp> getAllUserApps() {
        return userAppRepository.findAll();
    }

    /**
     * Get a UserApp entity by its ID.
     *
     * @param id the UserApp ID
     * @return an Optional containing the UserApp if found, or empty if not found
     */
    @Transactional(readOnly = true)
    public Optional<UserApp> getUserAppById(Long id) {
        return userAppRepository.findById(id);
    }

    /**
     * Get all UserApp entities for a given user.
     *
     * @param user the user
     * @return a list of UserApp entities
     */
    @Transactional(readOnly = true)
    public List<UserApp> getUserAppsByUser(User user) {
        return userAppRepository.findByUser(user);
    }

    /**
     * Get all UserApp entities for a given user ID.
     *
     * @param userId the user ID
     * @return a list of UserApp entities
     */
    @Transactional(readOnly = true)
    public List<UserApp> getUserAppsByUserId(Integer userId) {
        return userAppRepository.findByUserId(userId);
    }

    /**
     * Get all apps for a given user.
     *
     * @param user the user
     * @return a list of apps
     */
    @Transactional(readOnly = true)
    public List<App> getAppsByUser(User user) {
        return userAppRepository.findByUser(user)
                .stream()
                .map(UserApp::getApp)
                .filter(app -> app != null && app.getAppName() != null && !app.getAppName().isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * Get all apps for a given user ID.
     *
     * @param userId the user ID
     * @return a list of apps
     */
    @Transactional(readOnly = true)
    public List<App> getAppsByUserId(Integer userId) {
        return userAppRepository.findByUserId(userId)
                .stream()
                .map(UserApp::getApp)
                .filter(app -> app != null && app.getAppName() != null && !app.getAppName().isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * Get all UserApp entities for a given app.
     *
     * @param app the app
     * @return a list of UserApp entities
     */
    @Transactional(readOnly = true)
    public List<UserApp> getUserAppsByApp(App app) {
        return userAppRepository.findByApp(app);
    }

    /**
     * Get all UserApp entities for a given app ID.
     *
     * @param appId the app ID
     * @return a list of UserApp entities
     */
    @Transactional(readOnly = true)
    public List<UserApp> getUserAppsByAppId(Long appId) {
        return userAppRepository.findByAppId(appId);
    }

    /**
     * Get all UserApp entities for a given role level.
     *
     * @param roleLevel the role level
     * @return a list of UserApp entities
     */
    @Transactional(readOnly = true)
    public List<UserApp> getUserAppsByRoleLevel(ERole roleLevel) {
        return userAppRepository.findByRoleLevel(roleLevel);
    }

    /**
     * Get a UserApp entity for a given user and app.
     *
     * @param user the user
     * @param app the app
     * @return an Optional containing the UserApp if found, or empty if not found
     */
    @Transactional(readOnly = true)
    public Optional<UserApp> getUserAppByUserAndApp(User user, App app) {
        return userAppRepository.findByUserAndApp(user, app);
    }

    /**
     * Get a UserApp entity for a given user ID and app ID.
     *
     * @param userId the user ID
     * @param appId the app ID
     * @return an Optional containing the UserApp if found, or empty if not found
     */
    @Transactional(readOnly = true)
    public Optional<UserApp> getUserAppByUserIdAndAppId(Integer userId, Long appId) {
        return userAppRepository.findByUserIdAndAppId(userId, appId);
    }

    /**
     * Check if a user has access to a specific app.
     *
     * @param user the user
     * @param app the app
     * @return true if the user has access to the app, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean hasUserAccessToApp(User user, App app) {
        Optional<UserApp> userApp = userAppRepository.findByUserAndApp(user, app);
        return userApp.isPresent() && userApp.get().isEnabled();
    }

    /**
     * Check if a user has access to a specific app by IDs.
     *
     * @param userId the user ID
     * @param appId the app ID
     * @return true if the user has access to the app, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean hasUserAccessToApp(Integer userId, Long appId) {
        Optional<UserApp> userApp = userAppRepository.findByUserIdAndAppId(userId, appId);
        return userApp.isPresent() && userApp.get().isEnabled();
    }

    /**
     * Get the role level of a user for a specific app.
     *
     * @param user the user
     * @param app the app
     * @return an Optional containing the role level if the user has access to the app, or empty if not
     */
    @Transactional(readOnly = true)
    public Optional<ERole> getUserRoleLevelForApp(User user, App app) {
        Optional<UserApp> userApp = userAppRepository.findByUserAndApp(user, app);
        return userApp.map(UserApp::getRoleLevel);
    }

    /**
     * Get the role level of a user for a specific app by IDs.
     *
     * @param userId the user ID
     * @param appId the app ID
     * @return an Optional containing the role level if the user has access to the app, or empty if not
     */
    @Transactional(readOnly = true)
    public Optional<ERole> getUserRoleLevelForApp(Integer userId, Long appId) {
        Optional<UserApp> userApp = userAppRepository.findByUserIdAndAppId(userId, appId);
        return userApp.map(UserApp::getRoleLevel);
    }

    /**
     * Create a new UserApp entity.
     *
     * @param userApp the UserApp to create
     * @return the created UserApp
     */
    @Transactional
    public UserApp createUserApp(UserApp userApp) {
        return userAppRepository.save(userApp);
    }

    /**
     * Create a new UserApp entity from user, app, and role level.
     *
     * @param user the user
     * @param app the app
     * @param roleLevel the role level
     * @return the created UserApp
     */
    @Transactional
    public UserApp createUserApp(User user, App app, ERole roleLevel) {
        UserApp userApp = new UserApp(user, app, roleLevel);
        return userAppRepository.save(userApp);
    }

    /**
     * Create a new UserApp entity from user ID, app ID, and role level.
     *
     * @param userId the user ID
     * @param appId the app ID
     * @param roleLevel the role level
     * @return the created UserApp
     */
    @Transactional
    public UserApp createUserApp(Integer userId, Long appId, ERole roleLevel) {
        User user = userService.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        App app = appService.getAppById(appId)
                .orElseThrow(() -> new IllegalArgumentException("App not found with ID: " + appId));

        UserApp userApp = new UserApp(user, app, roleLevel);
        return userAppRepository.save(userApp);
    }

    /**
     * Update an existing UserApp entity.
     *
     * @param userApp the UserApp to update
     * @return the updated UserApp
     */
    @Transactional
    public UserApp updateUserApp(UserApp userApp) {
        return userAppRepository.save(userApp);
    }

    /**
     * Update the role level of a UserApp entity.
     *
     * @param id the UserApp ID
     * @param roleLevel the new role level
     * @return the updated UserApp
     */
    @Transactional
    public UserApp updateUserAppRoleLevel(Long id, ERole roleLevel) {
        UserApp userApp = userAppRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("UserApp not found with ID: " + id));
        userApp.setRoleLevel(roleLevel);
        return userAppRepository.save(userApp);
    }

    /**
     * Enable or disable a UserApp entity.
     *
     * @param id the UserApp ID
     * @param enabled the enabled status
     * @return the updated UserApp
     */
    @Transactional
    public UserApp setUserAppEnabled(Long id, boolean enabled) {
        UserApp userApp = userAppRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("UserApp not found with ID: " + id));
        userApp.setEnabled(enabled);
        return userAppRepository.save(userApp);
    }

    /**
     * Delete a UserApp entity by its ID.
     *
     * @param id the UserApp ID
     */
    @Transactional
    public void deleteUserApp(Long id) {
        userAppRepository.deleteById(id);
    }

    /**
     * Delete all UserApp entities for a given user.
     *
     * @param user the user
     */
    @Transactional
    public void deleteUserAppsByUser(User user) {
        userAppRepository.deleteByUser(user);
    }

    /**
     * Delete a UserApp entity for a given user and app.
     *
     * @param user the user
     * @param app the app
     */
    @Transactional
    public void deleteUserAppByUserAndApp(User user, App app) {
        userAppRepository.deleteByUserAndApp(user, app);
    }

    /**
     * Get all apps that a user has access to based on both company and user permissions.
     * A user can access an app only if:
     * 1. Their company has access to the app (CompanyApp exists and is enabled)
     * 2. The user has been granted access to the app (UserApp exists and is enabled)
     *
     * @param user the user
     * @return a list of apps the user can access
     */
    @Transactional(readOnly = true)
    public List<App> getAvailableAppsForUser(User user) {
        if (user == null || user.getCompany() == null || user.getCompany().trim().isEmpty()) {
            return List.of();
        }

        // Get the Company entity from the company name
        Optional<Company> companyOpt = companyService.getCompanyByName(user.getCompany());
        if (companyOpt.isEmpty()) {
            return List.of();
        }
        Company company = companyOpt.get();

        // Get apps available to the user's company
        List<App> companyApps = companyAppService.getAppsByCompany(company);

        // Get apps directly assigned to the user
        List<App> userApps = getAppsByUser(user);

        // Return intersection - apps that are both available to company AND assigned to user
        // Also filter out any apps with null appName
        return companyApps.stream()
                .filter(companyApp -> userApps.stream()
                        .anyMatch(userApp -> userApp.getId().equals(companyApp.getId())))
                .filter(app -> {
                    // Double-check that both relationships are enabled
                    boolean companyAppEnabled = companyAppService.hasCompanyAccessToApp(company, app);
                    boolean userAppEnabled = hasUserAccessToApp(user, app);
                    return companyAppEnabled && userAppEnabled;
                })
                .filter(app -> app != null && app.getAppName() != null && !app.getAppName().isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * Get all apps that a user has access to by user ID.
     *
     * @param userId the user ID
     * @return a list of apps the user can access
     */
    @Transactional(readOnly = true)
    public List<App> getAvailableAppsForUser(Integer userId) {
        User user = userService.findById(userId)
                .orElse(null);
        List<App> apps = getAvailableAppsForUser(user);
        // Double-check to ensure no apps with null appName make it through
        return apps.stream()
                .filter(app -> app != null && app.getAppName() != null && !app.getAppName().isEmpty())
                .collect(Collectors.toList());
    }
}
