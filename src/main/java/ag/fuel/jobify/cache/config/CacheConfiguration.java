package ag.fuel.jobify.cache.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Configuration for multi-level caching strategy.
 * Uses Caffeine for L1 (local) cache and Redis for L2 (distributed) cache.
 */
@Configuration
@EnableCaching
@RequiredArgsConstructor
public class CacheConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(CacheConfiguration.class);

    /**
     * Primary cache manager using Caffeine for high-performance local caching.
     * This is the L1 cache for frequently accessed data.
     */
    @Bean
    @Primary
    public CacheManager caffeineCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // Configure Caffeine cache with optimized settings
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(10_000) // Maximum 10,000 entries
            .expireAfterWrite(15, TimeUnit.MINUTES) // Expire after 15 minutes
            .expireAfterAccess(5, TimeUnit.MINUTES) // Expire after 5 minutes of no access
            .recordStats() // Enable statistics for monitoring
        );
        
        // Pre-configure cache names for better performance
        cacheManager.setCacheNames(List.of(
            "userApps",           // User's available apps
            "companyApps",        // Company's available apps
            "userPermissions",    // User permissions cache
            "appMetadata",        // App metadata cache
            "menuData",           // Menu rendering data
            "securityValidation"  // Security validation results
        ));
        
        LOGGER.info("Configured Caffeine cache manager with {} cache names", 
            cacheManager.getCacheNames().size());
        
        return cacheManager;
    }

    /**
     * Redis cache manager for distributed caching (L2 cache).
     * Used for data that needs to be shared across multiple instances.
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.host")
    public CacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        
        // Configure Redis serialization
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1)) // Default TTL of 1 hour
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()))
            .disableCachingNullValues(); // Don't cache null values

        // Configure specific cache settings
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // User apps cache - longer TTL since permissions don't change often
        cacheConfigurations.put("userApps", defaultConfig
            .entryTtl(Duration.ofHours(2))
            .prefixCacheNameWith("app:user:"));
        
        // Company apps cache - very long TTL since company permissions are stable
        cacheConfigurations.put("companyApps", defaultConfig
            .entryTtl(Duration.ofHours(6))
            .prefixCacheNameWith("app:company:"));
        
        // Security validation cache - shorter TTL for security
        cacheConfigurations.put("securityValidation", defaultConfig
            .entryTtl(Duration.ofMinutes(30))
            .prefixCacheNameWith("security:"));
        
        // Menu data cache - medium TTL
        cacheConfigurations.put("menuData", defaultConfig
            .entryTtl(Duration.ofHours(1))
            .prefixCacheNameWith("menu:"));

        RedisCacheManager cacheManager = RedisCacheManager.builder(redisConnectionFactory)
            .cacheDefaults(defaultConfig)
            .withInitialCacheConfigurations(cacheConfigurations)
            .transactionAware() // Enable transaction support
            .build();

        LOGGER.info("Configured Redis cache manager with {} specific cache configurations", 
            cacheConfigurations.size());

        return cacheManager;
    }

    /**
     * Cache warming configuration.
     * Defines which caches should be pre-loaded on application startup.
     */
    @Bean
    public CacheWarmupConfiguration cacheWarmupConfiguration() {
        return CacheWarmupConfiguration.builder()
            .warmupOnStartup(true)
            .warmupDelay(Duration.ofSeconds(30)) // Wait 30 seconds after startup
            .cacheNames("appMetadata", "companyApps") // Only warm up stable data
            .build();
    }

    /**
     * Configuration class for cache warmup settings.
     */
    public static class CacheWarmupConfiguration {
        private final boolean warmupOnStartup;
        private final Duration warmupDelay;
        private final String[] cacheNames;

        private CacheWarmupConfiguration(boolean warmupOnStartup, Duration warmupDelay, String[] cacheNames) {
            this.warmupOnStartup = warmupOnStartup;
            this.warmupDelay = warmupDelay;
            this.cacheNames = cacheNames;
        }

        public static Builder builder() {
            return new Builder();
        }

        public boolean isWarmupOnStartup() { return warmupOnStartup; }
        public Duration getWarmupDelay() { return warmupDelay; }
        public String[] getCacheNames() { return cacheNames; }

        public static class Builder {
            private boolean warmupOnStartup = false;
            private Duration warmupDelay = Duration.ofMinutes(1);
            private String[] cacheNames = new String[0];

            public Builder warmupOnStartup(boolean warmupOnStartup) {
                this.warmupOnStartup = warmupOnStartup;
                return this;
            }

            public Builder warmupDelay(Duration warmupDelay) {
                this.warmupDelay = warmupDelay;
                return this;
            }

            public Builder cacheNames(String... cacheNames) {
                this.cacheNames = cacheNames;
                return this;
            }

            public CacheWarmupConfiguration build() {
                return new CacheWarmupConfiguration(warmupOnStartup, warmupDelay, cacheNames);
            }
        }
    }
}
