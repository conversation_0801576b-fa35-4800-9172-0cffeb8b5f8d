package ag.fuel.jobify.cache.service;

import ag.fuel.jobify.app.service.AppService;
import ag.fuel.jobify.app.service.CompanyAppService;
import ag.fuel.jobify.app.service.UserAppService;
import ag.fuel.jobify.cache.config.CacheConfiguration;
import ag.fuel.jobify.company.service.CompanyService;
import ag.fuel.jobify.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.CompletableFuture;

/**
 * Service for warming up caches on application startup.
 * Pre-loads frequently accessed data to improve initial response times.
 */
@Service
@RequiredArgsConstructor
public class CacheWarmupService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CacheWarmupService.class);

    private final AppService appService;
    private final CompanyService companyService;
    private final CompanyAppService companyAppService;
    private final UserAppService userAppService;
    private final UserService userService;
    private final CacheConfiguration.CacheWarmupConfiguration warmupConfig;

    /**
     * Warm up caches after application startup.
     */
    @EventListener(ApplicationReadyEvent.class)
    @Async
    public void warmupCaches() {
        if (!warmupConfig.isWarmupOnStartup()) {
            LOGGER.info("Cache warmup is disabled");
            return;
        }

        LOGGER.info("Starting cache warmup in {} seconds...", warmupConfig.getWarmupDelay().getSeconds());
        
        try {
            // Wait for the configured delay
            Thread.sleep(warmupConfig.getWarmupDelay().toMillis());
            
            Instant startTime = Instant.now();
            
            // Warm up caches in parallel
            CompletableFuture<Void> appMetadataWarmup = warmupAppMetadata();
            CompletableFuture<Void> companyAppsWarmup = warmupCompanyApps();
            CompletableFuture<Void> frequentUserAppsWarmup = warmupFrequentUserApps();
            
            // Wait for all warmup tasks to complete
            CompletableFuture.allOf(appMetadataWarmup, companyAppsWarmup, frequentUserAppsWarmup)
                .join();
            
            Duration totalTime = Duration.between(startTime, Instant.now());
            LOGGER.info("Cache warmup completed in {} ms", totalTime.toMillis());
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.warn("Cache warmup was interrupted", e);
        } catch (Exception e) {
            LOGGER.error("Error during cache warmup", e);
        }
    }

    /**
     * Warm up app metadata cache.
     */
    @Async
    public CompletableFuture<Void> warmupAppMetadata() {
        try {
            LOGGER.debug("Warming up app metadata cache...");
            
            // Load all apps to cache app metadata
            var apps = appService.getAllApps();
            
            LOGGER.debug("Warmed up app metadata cache with {} apps", apps.size());
            return CompletableFuture.completedFuture(null);
            
        } catch (Exception e) {
            LOGGER.error("Error warming up app metadata cache", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * Warm up company apps cache.
     */
    @Async
    public CompletableFuture<Void> warmupCompanyApps() {
        try {
            LOGGER.debug("Warming up company apps cache...");
            
            // Load apps for all companies
            var companies = companyService.getAllCompanies(org.springframework.data.domain.Pageable.unpaged()).getContent();
            int warmedCompanies = 0;
            
            for (var company : companies) {
                try {
                    companyAppService.getAppsByCompany(company);
                    warmedCompanies++;
                } catch (Exception e) {
                    LOGGER.warn("Failed to warm up cache for company: {}", company.getCompanyName(), e);
                }
            }
            
            LOGGER.debug("Warmed up company apps cache for {}/{} companies", 
                warmedCompanies, companies.size());
            return CompletableFuture.completedFuture(null);
            
        } catch (Exception e) {
            LOGGER.error("Error warming up company apps cache", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * Warm up user apps cache for frequently accessed users.
     */
    @Async
    public CompletableFuture<Void> warmupFrequentUserApps() {
        try {
            LOGGER.debug("Warming up frequent user apps cache...");
            
            // Get recently active users (last 30 days)
            var recentUsers = userService.getRecentlyActiveUsers(30);
            int warmedUsers = 0;
            
            for (var user : recentUsers) {
                try {
                    userAppService.getAvailableAppsForUser(user);
                    warmedUsers++;
                } catch (Exception e) {
                    LOGGER.warn("Failed to warm up cache for user: {}", user.getEmail(), e);
                }
                
                // Limit to prevent overwhelming the system
                if (warmedUsers >= 100) {
                    break;
                }
            }
            
            LOGGER.debug("Warmed up user apps cache for {} users", warmedUsers);
            return CompletableFuture.completedFuture(null);
            
        } catch (Exception e) {
            LOGGER.error("Error warming up user apps cache", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * Manually trigger cache warmup (useful for testing or manual refresh).
     */
    public void manualWarmup() {
        LOGGER.info("Manual cache warmup triggered");
        warmupCaches();
    }

    /**
     * Get cache warmup statistics.
     */
    public CacheWarmupStats getWarmupStats() {
        // This would typically track warmup metrics
        return CacheWarmupStats.builder()
            .enabled(warmupConfig.isWarmupOnStartup())
            .lastWarmupTime(Instant.now()) // Placeholder
            .cacheNames(warmupConfig.getCacheNames())
            .build();
    }

    /**
     * Statistics about cache warmup operations.
     */
    public static class CacheWarmupStats {
        private final boolean enabled;
        private final Instant lastWarmupTime;
        private final String[] cacheNames;

        private CacheWarmupStats(boolean enabled, Instant lastWarmupTime, String[] cacheNames) {
            this.enabled = enabled;
            this.lastWarmupTime = lastWarmupTime;
            this.cacheNames = cacheNames;
        }

        public static Builder builder() {
            return new Builder();
        }

        public boolean isEnabled() { return enabled; }
        public Instant getLastWarmupTime() { return lastWarmupTime; }
        public String[] getCacheNames() { return cacheNames; }

        public static class Builder {
            private boolean enabled = false;
            private Instant lastWarmupTime = Instant.now();
            private String[] cacheNames = new String[0];

            public Builder enabled(boolean enabled) {
                this.enabled = enabled;
                return this;
            }

            public Builder lastWarmupTime(Instant lastWarmupTime) {
                this.lastWarmupTime = lastWarmupTime;
                return this;
            }

            public Builder cacheNames(String[] cacheNames) {
                this.cacheNames = cacheNames;
                return this;
            }

            public CacheWarmupStats build() {
                return new CacheWarmupStats(enabled, lastWarmupTime, cacheNames);
            }
        }
    }
}
