package ag.fuel.jobify.reference.runner;

import ag.fuel.jobify.reference.entity.Language;
import ag.fuel.jobify.reference.repository.LanguageRepository;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@RequiredArgsConstructor
public class LanguageRunner {

    @Value("${load-data-on-startup}")
    private boolean loadDataOnStartup;

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    private final LanguageRepository languageRepository;

    @PostConstruct
    public void init() {
        if (loadDataOnStartup) {
            languageRepository.deleteAll();

            languageRepository.saveAll(List.of(
                            new Language("pt", "Português"),
                            new Language("en", "Inglês")
                    )
            );
            LOGGER.info(" ########## < " + Language.class.getSimpleName() + " saved into Database > ########## ");
        } else {
            LOGGER.info(" ########## < " + Language.class.getSimpleName() + " (Skipped!) > ########## ");
        }
    }
}
