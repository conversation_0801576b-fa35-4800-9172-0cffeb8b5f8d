package ag.fuel.jobify.reference.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "job_language")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Language {
    
    @Id
    @Column(length = 2)
    private String code;

    @Column(length = 30)
    private String name;
}
