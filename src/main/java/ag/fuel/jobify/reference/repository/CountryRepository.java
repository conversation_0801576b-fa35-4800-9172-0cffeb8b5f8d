package ag.fuel.jobify.reference.repository;

import ag.fuel.jobify.reference.entity.Country;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CountryRepository extends ListCrudRepository<Country, String> {

    Optional<Country> findByCode(String code);
    
    List<Country> findAllByOrderByNameAsc();
}
