package ag.fuel.jobify.auth.controller;

import ag.fuel.jobify.security.entity.PasswordResetToken;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.communication.service.EmailService;
import ag.fuel.jobify.user.service.UserService;
import jakarta.mail.MessagingException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Locale;
import java.util.Optional;

@Controller
@RequiredArgsConstructor
public class PasswordResetController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PasswordResetController.class);

    private final UserService userService;
    private final MessageSource messageSource;
    private final EmailService emailService;

    // DTO for the reset password form
    public static class PasswordResetDto {
        @NotBlank(message = "{validation.password.notBlank}")
        @Size(min = 8, message = "{validation.password.size}")
        @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@#$!%*?&.;:<>])[A-Za-z\\d@#$!%*?&.;:<>]{8,}", message = "{validation.password.pattern}")
        private String newPassword;

        @NotBlank(message = "{validation.confirmPassword.notBlank}")
        private String confirmPassword;
        private String token;

        // Getters and Setters
        public String getNewPassword() { return newPassword; }
        public void setNewPassword(String newPassword) { this.newPassword = newPassword; }
        public String getConfirmPassword() { return confirmPassword; }
        public void setConfirmPassword(String confirmPassword) { this.confirmPassword = confirmPassword; }
        public String getToken() { return token; }
        public void setToken(String token) { this.token = token; }
    }

    @GetMapping("/forgot-password")
    public String showForgotPasswordPage() {
        return "user/forgot-password"; // Template is now in templates/user/
    }

    @PostMapping("/forgot-password")
    public String processForgotPassword(@RequestParam("email") String userEmail,
                                        HttpServletRequest request,
                                        RedirectAttributes redirectAttributes,
                                        Locale locale) {
        LOGGER.info("Password reset request received for email: {}", userEmail);

        Optional<User> userOptional = userService.findUserByEmail(userEmail);

        if (userOptional.isEmpty()) {
            LOGGER.warn("Password reset requested for non-existent email: {}", userEmail);
            redirectAttributes.addFlashAttribute("error", messageSource.getMessage("message.resetPassword.emailNotFound", null, locale));
            return "redirect:/forgot-password";
        }

        User user = userOptional.get();

        // This method will automatically delete any existing token for the user
        // and create a new one, preventing database constraint violations
        String token = userService.createPasswordResetTokenForUser(user);

        // Construct reset URL
        String appUrl = request.getScheme() + "://" + request.getServerName();
        if (request.getServerPort() != 80 && request.getServerPort() != 443) {
            appUrl += ":" + request.getServerPort();
        }
        appUrl += request.getContextPath();
        String resetUrl = appUrl + "/reset-password?token=" + token;

        String emailSubject = messageSource.getMessage("email.resetPassword.subject", null, "Password Reset Request", locale);

        try {
            emailService.sendPasswordResetEmail(user.getEmail(), emailSubject, resetUrl, locale);
            LOGGER.info("Password reset email sent successfully to: {}", userEmail);
            redirectAttributes.addFlashAttribute("success", messageSource.getMessage("message.resetPassword.linkSent", new Object[]{userEmail}, locale));
        } catch (MessagingException e) {
            LOGGER.error("Error sending password reset email to {}: {}", userEmail, e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", messageSource.getMessage("message.resetPassword.emailSendError", null, locale));
        }

        return "redirect:/forgot-password";
    }

    @GetMapping("/reset-password")
    public String showResetPasswordPage(@RequestParam("token") String token,
                                        @RequestParam(value = "completed", required = false) Boolean completed,
                                        Model model,
                                        RedirectAttributes redirectAttributes,
                                        Locale locale) {
        // If password reset is completed, we don't need to validate the token
        if (completed != null && completed) {
            model.addAttribute("completed", true);
            return "user/reset-password";
        }

        Optional<PasswordResetToken> resetTokenOptional = userService.getPasswordResetToken(token);

        if (resetTokenOptional.isEmpty() || resetTokenOptional.get().isExpired()) {
            redirectAttributes.addFlashAttribute("error", messageSource.getMessage("message.resetPassword.tokenInvalidOrExpired", null, locale));
            return "redirect:/forgot-password"; // Or a specific error page
        }

        model.addAttribute("token", token);
        model.addAttribute("passwordResetDto", new PasswordResetDto()); // For the form
        return "user/reset-password"; // Template is now in templates/user/
    }

    @PostMapping("/reset-password")
    public String processResetPassword(@Valid @ModelAttribute("passwordResetDto") PasswordResetDto passwordDto,
                                       BindingResult result,
                                       RedirectAttributes redirectAttributes,
                                       Locale locale) {
        if (result.hasErrors()) {
            // If using @Valid and have validation annotations in DTO, errors will be caught here
            // Pass the BindingResult to the redirect attributes so errors can be displayed on the form
            redirectAttributes.addFlashAttribute("org.springframework.validation.BindingResult.passwordResetDto", result);
            redirectAttributes.addFlashAttribute("passwordResetDto", passwordDto); // Keep submitted values
            return "redirect:/reset-password?token=" + passwordDto.getToken(); // Reshow form with errors
        }

        if (!passwordDto.getNewPassword().equals(passwordDto.getConfirmPassword())) {
            redirectAttributes.addFlashAttribute("error", messageSource.getMessage("message.resetPassword.passwordsDontMatch", null, locale));
            // It's better to add the error to BindingResult if possible, or pass it via Model to display next to fields
            return "redirect:/reset-password?token=" + passwordDto.getToken();
        }

        Optional<PasswordResetToken> tokenOptional = userService.getPasswordResetToken(passwordDto.getToken());

        if (tokenOptional.isEmpty() || tokenOptional.get().isExpired()) {
            redirectAttributes.addFlashAttribute("error", messageSource.getMessage("message.resetPassword.tokenInvalidOrExpired", null, locale));
            return "redirect:/forgot-password";
        }

        User user = tokenOptional.get().getUser();
        userService.changeUserPassword(user, passwordDto.getNewPassword());
        userService.deletePasswordResetToken(passwordDto.getToken()); // Invalidate the token

        LOGGER.info("Password successfully reset for user: {}", user.getEmail());
        redirectAttributes.addFlashAttribute("success", messageSource.getMessage("message.resetPassword.success", null, locale));
        return "redirect:/reset-password?token=" + passwordDto.getToken() + "&completed=true";
    }
}
