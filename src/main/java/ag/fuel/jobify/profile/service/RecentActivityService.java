package ag.fuel.jobify.profile.service;

import ag.fuel.jobify.common.service.DeviceInfoService;
import ag.fuel.jobify.common.util.Constants;
import ag.fuel.jobify.common.util.ExternalIPUtil;
import ag.fuel.jobify.common.util.GeoLocationUtils;
import ag.fuel.jobify.profile.entity.RecentActivity;
import ag.fuel.jobify.profile.repository.RecentActivityRepository;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.common.service.BrowserService;
import ag.fuel.jobify.common.service.DeviceService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class RecentActivityService {

    private final RecentActivityRepository recentActivityRepository;
    private final BrowserService browserService;
    private final DeviceInfoService deviceInfoService;

    public Page<RecentActivity> getRecentActivitiesByUserId(Long userId, Pageable pageable) {
        return recentActivityRepository.findByUserIdOrderByIdDesc(userId, pageable);
    }

    public List<RecentActivity> getRecentActivitiesByUserId(Long userId) {
        return recentActivityRepository.findByUserIdOrderByIdDesc(userId);
    }

    public Page<RecentActivity> getRecentActivitiesByUserIdWithFilters(Long userId, String dateFilter, String activityFilter, Pageable pageable) {
        return recentActivityRepository.findByUserIdWithFiltersOrderByIdDesc(userId, dateFilter, activityFilter, pageable);
    }

    public List<String> getDistinctActivitiesByUserId(Long userId) {
        return recentActivityRepository.findDistinctActivitiesByUserId(userId);
    }

    public RecentActivity saveRecentActivity(RecentActivity recentActivity) {
        return recentActivityRepository.save(recentActivity);
    }

    public void addRecentActivity(String userAgentStr, User user, String activity) {
        try {
            // Use DeviceInfoService to get the operating system and device type from the user agent string
            String operatingSystem = deviceInfoService.getOperatingSystemFromUserAgentString(userAgentStr);
            String deviceType = deviceInfoService.getDeviceTypeFromUserAgentString(userAgentStr);

            // Use BrowserService to get the browser from the user agent string
            String browser = browserService.getBrowserFromUserAgentString(userAgentStr);

            String externalIp = ExternalIPUtil.get();
            GeoLocationUtils geoLocationUtils = new GeoLocationUtils();
            /*Map<String, String> clientLocation = geoLocationUtils.getCountry(externalIp);*/
            Map<String, String> clientLocation = geoLocationUtils.getCountryAndCity(externalIp);

            RecentActivity recentActivity = new RecentActivity();
            recentActivity.setUserId(Long.valueOf(user.getId()));
            recentActivity.setBrowser(browser);
            recentActivity.setDeviceType(deviceType);
            recentActivity.setOperatingSystem(operatingSystem);
            recentActivity.setExternalIp(externalIp);
            recentActivity.setDateTime(LocalDateTime.now(Constants.BRASIL_TIME_ZONE).format(Constants.BRASIL_DATETIME_FORMAT_HHMM));
            recentActivity.setActivity(activity);

            recentActivity.setCountry(clientLocation.get(Constants.GEO_COUNTRY));
            recentActivity.setCity(clientLocation.get(Constants.GEO_CITY));

            recentActivityRepository.save(recentActivity);
        } catch (Exception e) {
            // Log error but don't fail the login process
            System.err.println("Failed to save recent activity: " + e.getMessage());
        }
    }
}
