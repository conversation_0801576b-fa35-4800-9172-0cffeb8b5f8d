package ag.fuel.jobify.common.config;

import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;

/**
 * Configuration for internationalization message sources.
 * Configures multiple resource bundles for core system messages and app-specific messages.
 */
@Configuration
public class MessageSourceConfiguration {

    /**
     * Configure MessageSource to load multiple resource bundles:
     * - messages: Core system functionality messages
     * - app_messages: Application-specific messages
     */
    @Bean
    public MessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        
        // Set multiple basenames for different message bundles
        messageSource.setBasenames(
            "classpath:messages",      // Core system messages
            "classpath:app_messages"   // App-specific messages
        );
        
        // Set default encoding
        messageSource.setDefaultEncoding("UTF-8");
        
        // Cache messages for performance (set to -1 for production, 0 for development)
        messageSource.setCacheSeconds(3600); // Cache for 1 hour
        
        // Fallback to system locale if specific locale not found
        messageSource.setFallbackToSystemLocale(false);
        
        return messageSource;
    }
}
