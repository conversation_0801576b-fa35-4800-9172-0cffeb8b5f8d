package ag.fuel.jobify.common.service;

import ag.fuel.jobify.common.util.UserAgentUtils;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Service for detecting and providing information about the end user's device and operating system.
 * Uses the UserAgentUtils class to parse and extract device and OS information.
 */
@Service
@RequiredArgsConstructor
public class DeviceInfoService {

    /**
     * Gets the operating system from the user agent string in the HTTP request.
     *
     * @param request The HTTP request containing the user agent header
     * @return The name of the operating system, or "Unknown" if it cannot be determined
     */
    public String getOperatingSystemFromRequest(HttpServletRequest request) {
        String userAgentString = request.getHeader("User-Agent");
        if (userAgentString == null || userAgentString.isEmpty()) {
            return "Unknown";
        }

        return getOperatingSystemFromUserAgentString(userAgentString);
    }

    /**
     * Gets the device type from the user agent string in the HTTP request.
     *
     * @param request The HTTP request containing the user agent header
     * @return The type of device, or "Unknown" if it cannot be determined
     */
    public String getDeviceTypeFromRequest(HttpServletRequest request) {
        String userAgentString = request.getHeader("User-Agent");
        if (userAgentString == null || userAgentString.isEmpty()) {
            return "Unknown";
        }

        return getDeviceTypeFromUserAgentString(userAgentString);
    }

    /**
     * Gets the operating system from a user agent string.
     *
     * @param userAgentString The user agent string
     * @return The name of the operating system, or "Unknown" if it cannot be determined
     */
    public String getOperatingSystemFromUserAgentString(String userAgentString) {
        if (userAgentString == null || userAgentString.isEmpty()) {
            return "Unknown";
        }

        // Special handling for iOS
        if (userAgentString.contains("iPhone") || userAgentString.contains("iPad") || userAgentString.contains("iPod")) {
            return "iOS";
        }

        // Special handling for Android
        if (userAgentString.contains("Android")) {
            return "Android";
        }

        // Special handling for Linux
        if (userAgentString.contains("Linux") && !userAgentString.contains("Android")) {
            return "Linux";
        }

        // Special handling for Mac
        if (userAgentString.contains("Mac OS X") && !userAgentString.contains("iPhone") && !userAgentString.contains("iPad") && !userAgentString.contains("iPod")) {
            return "Mac";
        }

        // Special handling for Windows
        if (userAgentString.contains("Windows")) {
            return "Windows";
        }

        // Default to using UserAgentUtils for other cases
        try {
            UserAgent userAgent = UserAgent.parseUserAgentString(userAgentString);
            OperatingSystem os = userAgent.getOperatingSystem();
            String osName = os.getName();

            // If the OS name contains a space, return the part before the space
            if (osName.contains(" ")) {
                return osName.substring(0, osName.indexOf(" "));
            }

            // Otherwise, return the full OS name
            return osName;
        } catch (Exception e) {
            return "Unknown";
        }
    }

    /**
     * Gets the device type from a user agent string.
     *
     * @param userAgentString The user agent string
     * @return The type of device, or "Unknown" if it cannot be determined
     */
    public String getDeviceTypeFromUserAgentString(String userAgentString) {
        if (userAgentString == null || userAgentString.isEmpty()) {
            return "Unknown";
        }

        UserAgent userAgent = UserAgent.parseUserAgentString(userAgentString);
        return UserAgentUtils.getDeviceType(userAgent);
    }
}
