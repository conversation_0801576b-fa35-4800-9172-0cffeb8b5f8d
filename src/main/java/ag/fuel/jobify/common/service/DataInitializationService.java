package ag.fuel.jobify.common.service;

import ag.fuel.jobify.app.entity.App;
import ag.fuel.jobify.app.entity.CompanyApp;
import ag.fuel.jobify.app.entity.UserApp;
import ag.fuel.jobify.app.service.AppService;
import ag.fuel.jobify.app.service.CompanyAppService;
import ag.fuel.jobify.app.service.UserAppService;
import ag.fuel.jobify.auth.entity.ERole;
import ag.fuel.jobify.auth.entity.Role;
import ag.fuel.jobify.auth.repository.RoleRepository;
import ag.fuel.jobify.company.entity.Company;
import ag.fuel.jobify.company.service.CompanyService;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.service.UserService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.Date;

/**
 * Service for initializing sample data to test the dynamic app menu system.
 * This service creates sample companies, users, and app relationships.
 */
@Service
@RequiredArgsConstructor
@ConfigurationProperties(prefix = "app.data-init")
public class DataInitializationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataInitializationService.class);

    private final AppService appService;
    private final CompanyService companyService;
    private final UserService userService;
    private final CompanyAppService companyAppService;
    private final UserAppService userAppService;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;

    private boolean enabled = false; // Can be configured via application.properties

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    /**
     * Initialize sample data if enabled
     */
    @PostConstruct
    public void initializeSampleData() {
        if (!enabled) {
            LOGGER.info("Sample data initialization is disabled");
            return;
        }

        LOGGER.info("Starting sample data initialization...");
        try {
            createSampleData();
            LOGGER.info("Sample data initialization completed successfully");
        } catch (Exception e) {
            LOGGER.error("Error during sample data initialization", e);
        }
    }

    /**
     * Create all sample data
     */
    @Transactional
    public void createSampleData() {
        // 1. Ensure apps exist (they should be created by AppService @PostConstruct)
        List<App> apps = ensureAppsExist();
        
        // 2. Create sample companies
        List<Company> companies = createSampleCompanies();
        
        // 3. Create company-app relationships
        createCompanyAppRelationships(companies, apps);
        
        // 4. Create sample users
        List<User> users = createSampleUsers(companies);
        
        // 5. Create user-app relationships
        createUserAppRelationships(users, apps);
        
        LOGGER.info("Created {} companies, {} users, and their app relationships", 
                companies.size(), users.size());
    }

    /**
     * Ensure all apps exist
     */
    private List<App> ensureAppsExist() {
        List<App> apps = appService.getAllApps();
        LOGGER.info("Found {} existing apps", apps.size());
        return apps;
    }

    /**
     * Create sample companies with different characteristics
     */
    private List<Company> createSampleCompanies() {
        List<Company> companies = new ArrayList<>();

        // Company 1: Full access company
        if (!companyService.getCompanyByName("TechCorp Solutions").isPresent()) {
            Company techCorp = new Company(
                "TechCorp Solutions",
                "123 Tech Street, Silicon Valley, CA",
                "United States",
                "12345678901",
                "techcorp.com"
            );
            companies.add(companyService.createCompany(companyService.toDto(techCorp)));
            LOGGER.info("Created company: TechCorp Solutions");
        }

        // Company 2: Limited access company
        if (!companyService.getCompanyByName("StartupHub Inc").isPresent()) {
            Company startupHub = new Company(
                "StartupHub Inc",
                "456 Innovation Ave, Austin, TX",
                "United States", 
                "23456789012",
                "startuphub.com"
            );
            companies.add(companyService.createCompany(companyService.toDto(startupHub)));
            LOGGER.info("Created company: StartupHub Inc");
        }

        // Company 3: Finance-focused company
        if (!companyService.getCompanyByName("Global Finance Ltd").isPresent()) {
            Company globalFinance = new Company(
                "Global Finance Ltd",
                "789 Finance Plaza, New York, NY",
                "United States",
                "34567890123", 
                "globalfinance.com"
            );
            companies.add(companyService.createCompany(companyService.toDto(globalFinance)));
            LOGGER.info("Created company: Global Finance Ltd");
        }

        return companies;
    }

    /**
     * Create company-app relationships
     */
    private void createCompanyAppRelationships(List<Company> companies, List<App> apps) {
        // TechCorp Solutions - Full access to all apps
        Company techCorp = companies.stream()
            .filter(c -> c.getCompanyName().equals("TechCorp Solutions"))
            .findFirst().orElse(null);
        if (techCorp != null) {
            for (App app : apps) {
                if (!companyAppService.hasCompanyAccessToApp(techCorp, app)) {
                    companyAppService.createCompanyApp(techCorp.getId(), app.getId());
                    LOGGER.debug("Granted {} access to {}", techCorp.getCompanyName(), app.getAppName());
                }
            }
        }

        // StartupHub Inc - Only Purchasing and Budgets
        Company startupHub = companies.stream()
            .filter(c -> c.getCompanyName().equals("StartupHub Inc"))
            .findFirst().orElse(null);
        if (startupHub != null) {
            apps.stream()
                .filter(app -> app.getAppName().equals("Purchasing") || app.getAppName().equals("Budgets"))
                .forEach(app -> {
                    if (!companyAppService.hasCompanyAccessToApp(startupHub, app)) {
                        companyAppService.createCompanyApp(startupHub.getId(), app.getId());
                        LOGGER.debug("Granted {} access to {}", startupHub.getCompanyName(), app.getAppName());
                    }
                });
        }

        // Global Finance Ltd - Only Finance and Budgets
        Company globalFinance = companies.stream()
            .filter(c -> c.getCompanyName().equals("Global Finance Ltd"))
            .findFirst().orElse(null);
        if (globalFinance != null) {
            apps.stream()
                .filter(app -> app.getAppName().equals("Finance") || app.getAppName().equals("Budgets"))
                .forEach(app -> {
                    if (!companyAppService.hasCompanyAccessToApp(globalFinance, app)) {
                        companyAppService.createCompanyApp(globalFinance.getId(), app.getId());
                        LOGGER.debug("Granted {} access to {}", globalFinance.getCompanyName(), app.getAppName());
                    }
                });
        }
    }

    /**
     * Create sample users with different roles and companies
     */
    private List<User> createSampleUsers(List<Company> companies) {
        List<User> users = new ArrayList<>();

        // Get roles
        Role userRole = roleRepository.findByName(ERole.ROLE_USER)
            .orElseThrow(() -> new RuntimeException("USER role not found"));
        Role moderatorRole = roleRepository.findByName(ERole.ROLE_MODERATOR)
            .orElseThrow(() -> new RuntimeException("MODERATOR role not found"));
        Role adminRole = roleRepository.findByName(ERole.ROLE_ADMIN)
            .orElseThrow(() -> new RuntimeException("ADMIN role not found"));

        // User 1: Admin user at TechCorp (full access)
        if (!userService.existsByEmail("<EMAIL>")) {
            User adminUser = createUser(
                "John Admin",
                "<EMAIL>",
                "TechCorp Solutions",
                adminRole,
                "United States"
            );
            users.add(adminUser);
            LOGGER.info("Created admin user: <EMAIL>");
        }

        // User 2: Moderator at TechCorp (full access)
        if (!userService.existsByEmail("<EMAIL>")) {
            User moderatorUser = createUser(
                "Jane Moderator",
                "<EMAIL>",
                "TechCorp Solutions",
                moderatorRole,
                "United States"
            );
            users.add(moderatorUser);
            LOGGER.info("Created moderator user: <EMAIL>");
        }

        // User 3: Regular user at TechCorp (limited access)
        if (!userService.existsByEmail("<EMAIL>")) {
            User regularUser = createUser(
                "Bob User",
                "<EMAIL>",
                "TechCorp Solutions",
                userRole,
                "United States"
            );
            users.add(regularUser);
            LOGGER.info("Created regular user: <EMAIL>");
        }

        // User 4: User at StartupHub (purchasing & budgets only)
        if (!userService.existsByEmail("<EMAIL>")) {
            User startupUser = createUser(
                "Alice Startup",
                "<EMAIL>",
                "StartupHub Inc",
                userRole,
                "United States"
            );
            users.add(startupUser);
            LOGGER.info("Created startup user: <EMAIL>");
        }

        // User 5: User at Global Finance (finance & budgets only)
        if (!userService.existsByEmail("<EMAIL>")) {
            User financeUser = createUser(
                "Charlie Finance",
                "<EMAIL>",
                "Global Finance Ltd",
                userRole,
                "United States"
            );
            users.add(financeUser);
            LOGGER.info("Created finance user: <EMAIL>");
        }

        return users;
    }

    /**
     * Helper method to create a user
     */
    private User createUser(String fullName, String email, String companyName, Role role, String country) {
        User user = new User();
        user.setFullName(fullName);
        user.setEmail(email);
        user.setPassword(passwordEncoder.encode("password123")); // Default password for testing
        user.setCompany(companyName);
        user.setCountry(country);
        user.setEnabled(true);
        user.setAccountLocked(false);
        user.setCreatedAt(new Date());
        user.setUpdatedAt(new Date());
        user.setRoles(Set.of(role));

        return userService.saveUser(user);
    }

    /**
     * Create user-app relationships based on business rules
     */
    private void createUserAppRelationships(List<User> users, List<App> apps) {
        for (User user : users) {
            String email = user.getEmail();

            if (email.contains("<EMAIL>")) {
                // Admin gets full access to all apps
                grantUserAccessToAllApps(user, apps, ERole.ROLE_ADMIN);
            } else if (email.contains("<EMAIL>")) {
                // Moderator gets full access to all apps
                grantUserAccessToAllApps(user, apps, ERole.ROLE_MODERATOR);
            } else if (email.contains("<EMAIL>")) {
                // Regular user gets limited access
                grantUserAccessToSpecificApps(user, apps, Arrays.asList("Purchasing", "Finance"), ERole.ROLE_USER);
            } else if (email.contains("<EMAIL>")) {
                // Startup user gets purchasing and budgets
                grantUserAccessToSpecificApps(user, apps, Arrays.asList("Purchasing", "Budgets"), ERole.ROLE_USER);
            } else if (email.contains("<EMAIL>")) {
                // Finance user gets finance and budgets
                grantUserAccessToSpecificApps(user, apps, Arrays.asList("Finance", "Budgets"), ERole.ROLE_USER);
            }
        }
    }

    /**
     * Grant user access to all available apps
     */
    private void grantUserAccessToAllApps(User user, List<App> apps, ERole roleLevel) {
        for (App app : apps) {
            if (!userAppService.hasUserAccessToApp(user, app)) {
                userAppService.createUserApp(user, app, roleLevel);
                LOGGER.debug("Granted {} access to {} with role {}", user.getEmail(), app.getAppName(), roleLevel);
            }
        }
    }

    /**
     * Grant user access to specific apps
     */
    private void grantUserAccessToSpecificApps(User user, List<App> apps, List<String> appNames, ERole roleLevel) {
        apps.stream()
            .filter(app -> appNames.contains(app.getAppName()))
            .forEach(app -> {
                if (!userAppService.hasUserAccessToApp(user, app)) {
                    userAppService.createUserApp(user, app, roleLevel);
                    LOGGER.debug("Granted {} access to {} with role {}", user.getEmail(), app.getAppName(), roleLevel);
                }
            });
    }

    /**
     * Method to clean up sample data (useful for testing)
     */
    @Transactional
    public void cleanupSampleData() {
        LOGGER.info("Cleaning up sample data...");

        List<String> sampleEmails = Arrays.asList(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        );

        for (String email : sampleEmails) {
            userService.findByEmail(email).ifPresent(user -> {
                // Clean up user-app relationships
                userAppService.deleteUserAppsByUser(user);
                LOGGER.debug("Cleaned up app relationships for user: {}", email);
            });
        }

        LOGGER.info("Sample data cleanup completed");
    }
}
