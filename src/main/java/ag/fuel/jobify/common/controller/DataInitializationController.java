package ag.fuel.jobify.common.controller;

import ag.fuel.jobify.common.service.DataInitializationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * REST Controller for managing sample data initialization.
 * This controller provides endpoints to create and clean up sample data for testing.
 */
@RestController
@RequestMapping("/api/admin/data-init")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
@Tag(name = "Data Initialization", description = "Manage sample data for testing")
public class DataInitializationController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataInitializationController.class);

    private final DataInitializationService dataInitializationService;

    /**
     * Create sample data for testing the dynamic app menu system
     */
    @PostMapping("/create")
    @Operation(summary = "Create sample data", description = "Creates sample companies, users, and app relationships for testing")
    public ResponseEntity<Map<String, String>> createSampleData() {
        LOGGER.info("Admin requested sample data creation");
        
        try {
            dataInitializationService.createSampleData();
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Sample data created successfully",
                "details", "Created companies: TechCorp Solutions, StartupHub Inc, Global Finance Ltd with users and app relationships"
            ));
        } catch (Exception e) {
            LOGGER.error("Error creating sample data", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "Failed to create sample data: " + e.getMessage()
            ));
        }
    }

    /**
     * Clean up sample data
     */
    @DeleteMapping("/cleanup")
    @Operation(summary = "Clean up sample data", description = "Removes sample data created for testing")
    public ResponseEntity<Map<String, String>> cleanupSampleData() {
        LOGGER.info("Admin requested sample data cleanup");
        
        try {
            dataInitializationService.cleanupSampleData();
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Sample data cleaned up successfully"
            ));
        } catch (Exception e) {
            LOGGER.error("Error cleaning up sample data", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "Failed to clean up sample data: " + e.getMessage()
            ));
        }
    }

    /**
     * Get information about sample data
     */
    @GetMapping("/info")
    @Operation(summary = "Get sample data info", description = "Returns information about the sample data that would be created")
    public ResponseEntity<Map<String, Object>> getSampleDataInfo() {
        return ResponseEntity.ok(Map.of(
            "companies", Map.of(
                "TechCorp Solutions", "Full access to all apps (Purchasing, Finance, Budgets)",
                "StartupHub Inc", "Access to Purchasing and Budgets only",
                "Global Finance Ltd", "Access to Finance and Budgets only"
            ),
            "users", Map.of(
                "<EMAIL>", "Admin user with full access to all apps (password: password123)",
                "<EMAIL>", "Moderator user with full access to all apps (password: password123)",
                "<EMAIL>", "Regular user with access to Purchasing and Finance (password: password123)",
                "<EMAIL>", "User with access to Purchasing and Budgets (password: password123)",
                "<EMAIL>", "User with access to Finance and Budgets (password: password123)"
            ),
            "note", "All users have the default password 'password123' for testing purposes"
        ));
    }
}
