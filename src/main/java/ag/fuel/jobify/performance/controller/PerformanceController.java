package ag.fuel.jobify.performance.controller;

import ag.fuel.jobify.cache.service.CacheWarmupService;
import ag.fuel.jobify.performance.service.PerformanceMonitoringService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * REST Controller for performance monitoring and cache management.
 * Provides endpoints for admins to monitor system performance and manage caches.
 */
@RestController
@RequestMapping("/api/admin/performance")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
@Tag(name = "Performance Monitoring", description = "Monitor and manage system performance")
public class PerformanceController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PerformanceController.class);

    private final PerformanceMonitoringService performanceMonitoringService;
    private final CacheWarmupService cacheWarmupService;

    /**
     * Get current performance metrics.
     */
    @GetMapping("/metrics")
    @Operation(summary = "Get performance metrics", description = "Returns current system performance metrics")
    public ResponseEntity<PerformanceMonitoringService.PerformanceMetrics> getMetrics() {
        LOGGER.debug("Admin requested performance metrics");
        
        try {
            PerformanceMonitoringService.PerformanceMetrics metrics = 
                performanceMonitoringService.getMetrics();
            
            return ResponseEntity.ok(metrics);
        } catch (Exception e) {
            LOGGER.error("Error retrieving performance metrics", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get cache statistics.
     */
    @GetMapping("/cache-stats")
    @Operation(summary = "Get cache statistics", description = "Returns detailed cache statistics")
    public ResponseEntity<Map<String, PerformanceMonitoringService.CacheStats>> getCacheStats() {
        LOGGER.debug("Admin requested cache statistics");
        
        try {
            Map<String, PerformanceMonitoringService.CacheStats> stats = 
                performanceMonitoringService.getCacheStatistics();
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            LOGGER.error("Error retrieving cache statistics", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Trigger manual cache warmup.
     */
    @PostMapping("/cache-warmup")
    @Operation(summary = "Trigger cache warmup", description = "Manually triggers cache warmup process")
    public ResponseEntity<Map<String, String>> triggerCacheWarmup() {
        LOGGER.info("Admin triggered manual cache warmup");
        
        try {
            cacheWarmupService.manualWarmup();
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Cache warmup triggered successfully"
            ));
        } catch (Exception e) {
            LOGGER.error("Error triggering cache warmup", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "Failed to trigger cache warmup: " + e.getMessage()
            ));
        }
    }

    /**
     * Get cache warmup status.
     */
    @GetMapping("/cache-warmup/status")
    @Operation(summary = "Get cache warmup status", description = "Returns cache warmup configuration and status")
    public ResponseEntity<CacheWarmupService.CacheWarmupStats> getCacheWarmupStatus() {
        LOGGER.debug("Admin requested cache warmup status");
        
        try {
            CacheWarmupService.CacheWarmupStats stats = cacheWarmupService.getWarmupStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            LOGGER.error("Error retrieving cache warmup status", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Reset performance metrics.
     */
    @PostMapping("/metrics/reset")
    @Operation(summary = "Reset performance metrics", description = "Resets all performance counters and metrics")
    public ResponseEntity<Map<String, String>> resetMetrics() {
        LOGGER.info("Admin requested performance metrics reset");
        
        try {
            performanceMonitoringService.resetMetrics();
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Performance metrics reset successfully"
            ));
        } catch (Exception e) {
            LOGGER.error("Error resetting performance metrics", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "Failed to reset metrics: " + e.getMessage()
            ));
        }
    }

    /**
     * Generate performance report.
     */
    @PostMapping("/report")
    @Operation(summary = "Generate performance report", description = "Generates and logs a comprehensive performance report")
    public ResponseEntity<Map<String, String>> generatePerformanceReport() {
        LOGGER.info("Admin requested performance report generation");
        
        try {
            performanceMonitoringService.logPerformanceSummary();
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Performance report generated and logged successfully"
            ));
        } catch (Exception e) {
            LOGGER.error("Error generating performance report", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "Failed to generate report: " + e.getMessage()
            ));
        }
    }

    /**
     * Get system health check.
     */
    @GetMapping("/health")
    @Operation(summary = "Get system health", description = "Returns system health indicators")
    public ResponseEntity<Map<String, Object>> getSystemHealth() {
        try {
            PerformanceMonitoringService.PerformanceMetrics metrics = 
                performanceMonitoringService.getMetrics();
            
            // Simple health indicators
            Map<String, Object> health = Map.of(
                "status", "UP",
                "cache_hit_rate", metrics.getGeneralMetrics().get("cache_hit_rate"),
                "total_menu_loads", metrics.getGeneralMetrics().get("menu_loads"),
                "timestamp", metrics.getTimestamp()
            );
            
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            LOGGER.error("Error checking system health", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "DOWN",
                "error", e.getMessage()
            ));
        }
    }
}
