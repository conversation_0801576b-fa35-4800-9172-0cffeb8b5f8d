package ag.fuel.jobify.performance.service;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Service for monitoring performance metrics related to the app menu system.
 * Tracks cache hit rates, response times, and other performance indicators.
 */
@Service
@RequiredArgsConstructor
public class PerformanceMonitoringService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PerformanceMonitoringService.class);
    private static final Logger PERF_LOGGER = LoggerFactory.getLogger("PERFORMANCE_METRICS");

    private final CacheManager cacheManager;

    // Performance counters
    private final AtomicLong menuLoadCount = new AtomicLong(0);
    private final AtomicLong cacheHitCount = new AtomicLong(0);
    private final AtomicLong cacheMissCount = new AtomicLong(0);
    private final Map<String, AtomicLong> operationCounts = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> operationTimes = new ConcurrentHashMap<>();

    /**
     * Record menu load operation.
     */
    public void recordMenuLoad(String userId, Duration loadTime) {
        menuLoadCount.incrementAndGet();
        recordOperation("menu_load", loadTime);
        
        PERF_LOGGER.debug("MENU_LOAD | User: {} | Duration: {}ms | Total: {}",
            userId, loadTime.toMillis(), menuLoadCount.get());
    }

    /**
     * Record cache hit.
     */
    public void recordCacheHit(String cacheName, String key) {
        cacheHitCount.incrementAndGet();
        recordOperation("cache_hit_" + cacheName, Duration.ZERO);
        
        PERF_LOGGER.debug("CACHE_HIT | Cache: {} | Key: {} | Total: {}", 
            cacheName, key, cacheHitCount.get());
    }

    /**
     * Record cache miss.
     */
    public void recordCacheMiss(String cacheName, String key) {
        cacheMissCount.incrementAndGet();
        recordOperation("cache_miss_" + cacheName, Duration.ZERO);
        
        PERF_LOGGER.debug("CACHE_MISS | Cache: {} | Key: {} | Total: {}", 
            cacheName, key, cacheMissCount.get());
    }

    /**
     * Record database query operation.
     */
    public void recordDatabaseQuery(String queryType, Duration queryTime) {
        recordOperation("db_query_" + queryType, queryTime);
        
        if (queryTime.toMillis() > 100) { // Log slow queries
            PERF_LOGGER.warn("SLOW_QUERY | Type: {} | Duration: {}ms", 
                queryType, queryTime.toMillis());
        }
    }

    /**
     * Record security validation operation.
     */
    public void recordSecurityValidation(String userId, String appName, Duration validationTime, boolean allowed) {
        recordOperation("security_validation", validationTime);
        
        PERF_LOGGER.info("SECURITY_VALIDATION | User: {} | App: {} | Duration: {}ms | Allowed: {}", 
            userId, appName, validationTime.toMillis(), allowed);
    }

    /**
     * Record generic operation.
     */
    private void recordOperation(String operationType, Duration duration) {
        operationCounts.computeIfAbsent(operationType, k -> new AtomicLong(0)).incrementAndGet();
        operationTimes.computeIfAbsent(operationType, k -> new AtomicLong(0))
            .addAndGet(duration.toMillis());
    }

    /**
     * Get performance metrics summary.
     */
    public PerformanceMetrics getMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        // Cache metrics
        long totalCacheOperations = cacheHitCount.get() + cacheMissCount.get();
        double cacheHitRate = totalCacheOperations > 0 ? 
            (double) cacheHitCount.get() / totalCacheOperations * 100 : 0;
        
        metrics.put("cache_hit_rate", String.format("%.2f%%", cacheHitRate));
        metrics.put("cache_hits", cacheHitCount.get());
        metrics.put("cache_misses", cacheMissCount.get());
        metrics.put("menu_loads", menuLoadCount.get());
        
        // Operation metrics
        Map<String, OperationMetric> operationMetrics = new HashMap<>();
        for (String operation : operationCounts.keySet()) {
            long count = operationCounts.get(operation).get();
            long totalTime = operationTimes.get(operation).get();
            double avgTime = count > 0 ? (double) totalTime / count : 0;
            
            operationMetrics.put(operation, new OperationMetric(count, totalTime, avgTime));
        }
        
        return new PerformanceMetrics(metrics, operationMetrics, Instant.now());
    }

    /**
     * Get cache statistics.
     */
    public Map<String, CacheStats> getCacheStatistics() {
        Map<String, CacheStats> stats = new HashMap<>();
        
        for (String cacheName : cacheManager.getCacheNames()) {
            var cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                // Basic cache info (implementation-specific stats would require casting)
                stats.put(cacheName, new CacheStats(cacheName, 0, 0, 0)); // Placeholder
            }
        }
        
        return stats;
    }

    /**
     * Reset all metrics (useful for testing).
     */
    public void resetMetrics() {
        menuLoadCount.set(0);
        cacheHitCount.set(0);
        cacheMissCount.set(0);
        operationCounts.clear();
        operationTimes.clear();
        
        LOGGER.info("Performance metrics reset");
    }

    /**
     * Log performance summary.
     */
    public void logPerformanceSummary() {
        PerformanceMetrics metrics = getMetrics();
        
        PERF_LOGGER.info("=== PERFORMANCE SUMMARY ===");
        PERF_LOGGER.info("Cache Hit Rate: {}", metrics.getGeneralMetrics().get("cache_hit_rate"));
        PERF_LOGGER.info("Total Menu Loads: {}", metrics.getGeneralMetrics().get("menu_loads"));
        PERF_LOGGER.info("Cache Hits: {}", metrics.getGeneralMetrics().get("cache_hits"));
        PERF_LOGGER.info("Cache Misses: {}", metrics.getGeneralMetrics().get("cache_misses"));
        
        metrics.getOperationMetrics().forEach((operation, metric) -> {
            PERF_LOGGER.info("Operation: {} | Count: {} | Avg Time: {:.2f}ms", 
                operation, metric.getCount(), metric.getAverageTime());
        });
        
        PERF_LOGGER.info("=== END SUMMARY ===");
    }

    /**
     * Performance metrics container.
     */
    public static class PerformanceMetrics {
        private final Map<String, Object> generalMetrics;
        private final Map<String, OperationMetric> operationMetrics;
        private final Instant timestamp;

        public PerformanceMetrics(Map<String, Object> generalMetrics, 
                                Map<String, OperationMetric> operationMetrics, 
                                Instant timestamp) {
            this.generalMetrics = generalMetrics;
            this.operationMetrics = operationMetrics;
            this.timestamp = timestamp;
        }

        public Map<String, Object> getGeneralMetrics() { return generalMetrics; }
        public Map<String, OperationMetric> getOperationMetrics() { return operationMetrics; }
        public Instant getTimestamp() { return timestamp; }
    }

    /**
     * Operation metric details.
     */
    public static class OperationMetric {
        private final long count;
        private final long totalTime;
        private final double averageTime;

        public OperationMetric(long count, long totalTime, double averageTime) {
            this.count = count;
            this.totalTime = totalTime;
            this.averageTime = averageTime;
        }

        public long getCount() { return count; }
        public long getTotalTime() { return totalTime; }
        public double getAverageTime() { return averageTime; }
    }

    /**
     * Cache statistics.
     */
    public static class CacheStats {
        private final String name;
        private final long size;
        private final long hitCount;
        private final long missCount;

        public CacheStats(String name, long size, long hitCount, long missCount) {
            this.name = name;
            this.size = size;
            this.hitCount = hitCount;
            this.missCount = missCount;
        }

        public String getName() { return name; }
        public long getSize() { return size; }
        public long getHitCount() { return hitCount; }
        public long getMissCount() { return missCount; }
        public double getHitRate() { 
            long total = hitCount + missCount;
            return total > 0 ? (double) hitCount / total * 100 : 0;
        }
    }
}
