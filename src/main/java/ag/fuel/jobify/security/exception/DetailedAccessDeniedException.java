package ag.fuel.jobify.security.exception;

import ag.fuel.jobify.auth.entity.ERole;
import ag.fuel.jobify.user.entity.User;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.access.AccessDeniedException;

/**
 * Custom access denied exception that provides detailed information about why access was denied.
 * This helps create user-friendly error messages with specific role requirements.
 */
public class DetailedAccessDeniedException extends AccessDeniedException {

    private final String userEmail;
    private final String appName;
    private final ERole userRole;
    private final ERole requiredRole;
    private final AccessDeniedReason reason;
    private static MessageSource messageSource;

    public DetailedAccessDeniedException(String message, String userEmail, String appName, 
                                       ERole userRole, ERole requiredRole, AccessDeniedReason reason) {
        super(message);
        this.userEmail = userEmail;
        this.appName = appName;
        this.userRole = userRole;
        this.requiredRole = requiredRole;
        this.reason = reason;
    }

    public DetailedAccessDeniedException(String message, AccessDeniedReason reason) {
        super(message);
        this.userEmail = null;
        this.appName = null;
        this.userRole = null;
        this.requiredRole = null;
        this.reason = reason;
    }

    // Getters
    public String getUserEmail() { return userEmail; }
    public String getAppName() { return appName; }
    public ERole getUserRole() { return userRole; }
    public ERole getRequiredRole() { return requiredRole; }
    public AccessDeniedReason getReason() { return reason; }

    /**
     * Set the message source for internationalization.
     */
    public static void setMessageSource(MessageSource messageSource) {
        DetailedAccessDeniedException.messageSource = messageSource;
    }

    /**
     * Get a user-friendly error message based on the denial reason.
     */
    public String getUserFriendlyMessage() {
        if (messageSource == null) {
            return getFallbackMessage();
        }

        try {
            switch (reason) {
                case INSUFFICIENT_ROLE:
                    return messageSource.getMessage("access.denied.insufficient.role",
                        new Object[]{userEmail,
                                   userRole != null ? userRole.name() : "UNKNOWN",
                                   requiredRole != null ? requiredRole.name() : "UNKNOWN",
                                   appName},
                        LocaleContextHolder.getLocale());

                case NO_APP_ACCESS:
                    return messageSource.getMessage("access.denied.no.app.access",
                        new Object[]{userEmail, appName},
                        LocaleContextHolder.getLocale());

                case COMPANY_NO_ACCESS:
                    return messageSource.getMessage("access.denied.company.no.access",
                        new Object[]{appName},
                        LocaleContextHolder.getLocale());

                case USER_DISABLED:
                    return messageSource.getMessage("access.denied.user.disabled",
                        new Object[]{userEmail},
                        LocaleContextHolder.getLocale());

                case USER_LOCKED:
                    return messageSource.getMessage("access.denied.user.locked",
                        new Object[]{userEmail},
                        LocaleContextHolder.getLocale());

                case APP_DISABLED:
                    return messageSource.getMessage("access.denied.app.disabled",
                        new Object[]{appName},
                        LocaleContextHolder.getLocale());

                case APP_NOT_FOUND:
                    return messageSource.getMessage("access.denied.app.not.found",
                        new Object[]{appName},
                        LocaleContextHolder.getLocale());

                case NOT_AUTHENTICATED:
                    return messageSource.getMessage("access.denied.not.authenticated",
                        null,
                        LocaleContextHolder.getLocale());

                default:
                    return messageSource.getMessage("access.denied.unknown",
                        null,
                        LocaleContextHolder.getLocale());
            }
        } catch (Exception e) {
            return getFallbackMessage();
        }
    }

    /**
     * Fallback message when internationalization is not available.
     */
    private String getFallbackMessage() {
        switch (reason) {
            case INSUFFICIENT_ROLE:
                return String.format("Access denied: User %s has role %s but requires %s for app %s",
                    userEmail,
                    userRole != null ? userRole.name() : "UNKNOWN",
                    requiredRole != null ? requiredRole.name() : "UNKNOWN",
                    appName);

            case NO_APP_ACCESS:
                return String.format("Access denied: User %s does not have access to app %s",
                    userEmail, appName);

            case COMPANY_NO_ACCESS:
                return String.format("Access denied: Your company does not have access to app %s",
                    appName);

            case USER_DISABLED:
                return String.format("Access denied: User account %s is disabled", userEmail);

            case USER_LOCKED:
                return String.format("Access denied: User account %s is locked", userEmail);

            case APP_DISABLED:
                return String.format("Access denied: Application %s is currently disabled", appName);

            case APP_NOT_FOUND:
                return String.format("Access denied: Application %s does not exist", appName);

            case NOT_AUTHENTICATED:
                return "Access denied: You must be logged in to access this application";

            default:
                return "Access denied: Insufficient permissions for this application";
        }
    }

    /**
     * Get a detailed explanation for the user.
     */
    public String getDetailedExplanation() {
        switch (reason) {
            case INSUFFICIENT_ROLE:
                return String.format("You currently have %s permissions, but this operation requires %s level access. " +
                    "Contact your administrator to request elevated permissions.",
                    getRoleDisplayName(userRole), getRoleDisplayName(requiredRole));
            
            case NO_APP_ACCESS:
                return "You haven't been granted access to this application. " +
                    "Contact your administrator to request access.";
            
            case COMPANY_NO_ACCESS:
                return "Your organization doesn't have a subscription or license for this application. " +
                    "Contact your company administrator to add this application to your plan.";
            
            case USER_DISABLED:
                return "Your user account has been disabled. " +
                    "Contact your administrator to reactivate your account.";
            
            case USER_LOCKED:
                return "Your user account has been temporarily locked. " +
                    "Contact your administrator to unlock your account.";
            
            case APP_DISABLED:
                return "This application is temporarily unavailable for maintenance. " +
                    "Please try again later or contact support if the issue persists.";
            
            case APP_NOT_FOUND:
                return "The application you're trying to access doesn't exist or has been removed. " +
                    "Check the URL or contact support for assistance.";
            
            case NOT_AUTHENTICATED:
                return "Please log in with your credentials to access this application.";
            
            default:
                return "You don't have the necessary permissions to access this resource. " +
                    "Contact your administrator for assistance.";
        }
    }

    /**
     * Get suggested actions for the user.
     */
    public String getSuggestedAction() {
        switch (reason) {
            case INSUFFICIENT_ROLE:
                return "Request role upgrade from your administrator";
            
            case NO_APP_ACCESS:
                return "Request application access from your administrator";
            
            case COMPANY_NO_ACCESS:
                return "Contact your company administrator to add this application";
            
            case USER_DISABLED:
            case USER_LOCKED:
                return "Contact your administrator to resolve account issues";
            
            case APP_DISABLED:
                return "Try again later or contact support";
            
            case APP_NOT_FOUND:
                return "Verify the application URL or contact support";
            
            case NOT_AUTHENTICATED:
                return "Log in to your account";
            
            default:
                return "Contact your administrator for assistance";
        }
    }

    /**
     * Get user-friendly role display name.
     */
    private String getRoleDisplayName(ERole role) {
        if (role == null) return "Unknown";
        
        switch (role) {
            case ROLE_USER:
                return "User";
            case ROLE_MODERATOR:
                return "Moderator";
            case ROLE_ADMIN:
                return "Administrator";
            default:
                return role.name().replace("ROLE_", "");
        }
    }

    /**
     * Enumeration of possible access denied reasons.
     */
    public enum AccessDeniedReason {
        INSUFFICIENT_ROLE,      // User has lower role than required
        NO_APP_ACCESS,          // User doesn't have access to the app
        COMPANY_NO_ACCESS,      // Company doesn't have access to the app
        USER_DISABLED,          // User account is disabled
        USER_LOCKED,            // User account is locked
        APP_DISABLED,           // Application is disabled
        APP_NOT_FOUND,          // Application doesn't exist
        NOT_AUTHENTICATED,      // User is not logged in
        UNKNOWN                 // Unknown reason
    }
}
