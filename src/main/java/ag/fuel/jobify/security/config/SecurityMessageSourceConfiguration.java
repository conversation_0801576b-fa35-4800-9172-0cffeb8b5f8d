package ag.fuel.jobify.security.config;

import ag.fuel.jobify.security.exception.DetailedAccessDeniedException;
import lombok.RequiredArgsConstructor;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * Configuration to inject MessageSource into security exception classes
 * for internationalization support.
 */
@Configuration
@RequiredArgsConstructor
public class SecurityMessageSourceConfiguration {

    private final MessageSource messageSource;

    @PostConstruct
    public void configureMessageSource() {
        DetailedAccessDeniedException.setMessageSource(messageSource);
    }
}
