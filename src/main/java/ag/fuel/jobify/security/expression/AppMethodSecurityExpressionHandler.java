package ag.fuel.jobify.security.expression;

import ag.fuel.jobify.app.service.AppService;
import ag.fuel.jobify.app.service.UserAppService;
import ag.fuel.jobify.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.context.ApplicationContext;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.access.expression.method.MethodSecurityExpressionOperations;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

/**
 * Custom method security expression handler for app-specific security expressions.
 */
@Component
@RequiredArgsConstructor
public class AppMethodSecurityExpressionHandler extends DefaultMethodSecurityExpressionHandler {

    private final UserService userService;
    private final AppService appService;
    private final UserAppService userAppService;
    private final ApplicationContext applicationContext;

    @Override
    protected MethodSecurityExpressionOperations createSecurityExpressionRoot(
            Authentication authentication, MethodInvocation invocation) {

        AppSecurityExpressionRoot root = new AppSecurityExpressionRoot(
            authentication, userService, appService, userAppService);
        root.setPermissionEvaluator(getPermissionEvaluator());
        root.setTrustResolver(getTrustResolver());
        root.setRoleHierarchy(getRoleHierarchy());
        root.setDefaultRolePrefix(getDefaultRolePrefix());

        return root;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        super.setApplicationContext(applicationContext);
    }
}
