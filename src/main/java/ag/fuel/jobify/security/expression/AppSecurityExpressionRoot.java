package ag.fuel.jobify.security.expression;

import ag.fuel.jobify.app.entity.App;
import ag.fuel.jobify.app.service.AppService;
import ag.fuel.jobify.app.service.UserAppService;
import ag.fuel.jobify.auth.entity.ERole;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.expression.SecurityExpressionRoot;
import org.springframework.security.access.expression.method.MethodSecurityExpressionOperations;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Custom security expression root for app-specific security checks.
 * Provides methods to check if users have access to specific apps.
 */
public class AppSecurityExpressionRoot extends SecurityExpressionRoot implements MethodSecurityExpressionOperations {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppSecurityExpressionRoot.class);

    private final UserService userService;
    private final AppService appService;
    private final UserAppService userAppService;

    private Object filterObject;
    private Object returnObject;

    public AppSecurityExpressionRoot(Authentication authentication, UserService userService,
                                   AppService appService, UserAppService userAppService) {
        super(authentication);
        this.userService = userService;
        this.appService = appService;
        this.userAppService = userAppService;
    }

    /**
     * Check if the current user has access to a specific app by name.
     *
     * @param appName the name of the app to check access for
     * @return true if the user has access, false otherwise
     */
    public boolean hasAppAccess(String appName) {
        try {
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                LOGGER.warn("No current user found for app access check: {}", appName);
                return false;
            }

            // Get the app by name
            Optional<App> appOpt = appService.getAppByName(capitalizeFirstLetter(appName));
            if (appOpt.isEmpty()) {
                LOGGER.warn("App not found: {}", appName);
                return false;
            }

            App app = appOpt.get();
            boolean hasAccess = userAppService.hasUserAccessToApp(currentUser, app);
            
            LOGGER.debug("User {} access check for app {}: {}", 
                currentUser.getEmail(), appName, hasAccess);
            
            return hasAccess;
        } catch (Exception e) {
            LOGGER.error("Error checking app access for {}: {}", appName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Check if the current user has access to a specific app with a minimum role level.
     *
     * @param appName the name of the app to check access for
     * @param minRole the minimum role required (USER, MODERATOR, ADMIN)
     * @return true if the user has access with the required role, false otherwise
     */
    public boolean hasAppAccessWithRole(String appName, String minRole) {
        try {
            if (!hasAppAccess(appName)) {
                return false;
            }

            User currentUser = userService.getCurrentUser();
            Optional<App> appOpt = appService.getAppByName(capitalizeFirstLetter(appName));
            
            if (currentUser == null || appOpt.isEmpty()) {
                return false;
            }

            // Get user's role level for this app
            Optional<ERole> userRoleOpt = userAppService.getUserRoleLevelForApp(currentUser, appOpt.get());
            if (userRoleOpt.isEmpty()) {
                return false;
            }

            ERole userRole = userRoleOpt.get();
            ERole requiredRole = ERole.valueOf("ROLE_" + minRole.toUpperCase());

            // Check if user's role meets the minimum requirement
            boolean hasRequiredRole = hasMinimumRole(userRole, requiredRole);
            
            LOGGER.debug("User {} role check for app {} (required: {}, user has: {}): {}", 
                currentUser.getEmail(), appName, requiredRole, userRole, hasRequiredRole);
            
            return hasRequiredRole;
        } catch (Exception e) {
            LOGGER.error("Error checking app access with role for {} (role: {}): {}", 
                appName, minRole, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Check if the current user is an admin for a specific app.
     *
     * @param appName the name of the app to check admin access for
     * @return true if the user is an admin for the app, false otherwise
     */
    public boolean isAppAdmin(String appName) {
        return hasAppAccessWithRole(appName, "ADMIN");
    }

    /**
     * Check if the current user is a moderator or admin for a specific app.
     *
     * @param appName the name of the app to check moderator access for
     * @return true if the user is a moderator or admin for the app, false otherwise
     */
    public boolean isAppModerator(String appName) {
        return hasAppAccessWithRole(appName, "MODERATOR");
    }

    /**
     * Check if the current user has any app access (used for general app area access).
     *
     * @return true if the user has access to at least one app, false otherwise
     */
    public boolean hasAnyAppAccess() {
        try {
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return false;
            }

            boolean hasAnyAccess = !userAppService.getAvailableAppsForUser(currentUser).isEmpty();
            
            LOGGER.debug("User {} has any app access: {}", currentUser.getEmail(), hasAnyAccess);
            
            return hasAnyAccess;
        } catch (Exception e) {
            LOGGER.error("Error checking any app access: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Check if a role meets the minimum requirement.
     *
     * @param userRole the user's role
     * @param requiredRole the minimum required role
     * @return true if the user's role meets the requirement
     */
    private boolean hasMinimumRole(ERole userRole, ERole requiredRole) {
        // Role hierarchy: USER < MODERATOR < ADMIN
        int userLevel = getRoleLevel(userRole);
        int requiredLevel = getRoleLevel(requiredRole);
        
        return userLevel >= requiredLevel;
    }

    /**
     * Get the numeric level of a role for comparison.
     *
     * @param role the role to get the level for
     * @return the numeric level (higher = more privileged)
     */
    private int getRoleLevel(ERole role) {
        switch (role) {
            case ROLE_USER:
                return 1;
            case ROLE_MODERATOR:
                return 2;
            case ROLE_ADMIN:
                return 3;
            default:
                return 0;
        }
    }

    /**
     * Utility method to capitalize the first letter of a string.
     *
     * @param str the string to capitalize
     * @return the capitalized string
     */
    private String capitalizeFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }

    @Override
    public void setFilterObject(Object filterObject) {
        this.filterObject = filterObject;
    }

    @Override
    public Object getFilterObject() {
        return filterObject;
    }

    @Override
    public void setReturnObject(Object returnObject) {
        this.returnObject = returnObject;
    }

    @Override
    public Object getReturnObject() {
        return returnObject;
    }

    @Override
    public Object getThis() {
        return this;
    }
}
