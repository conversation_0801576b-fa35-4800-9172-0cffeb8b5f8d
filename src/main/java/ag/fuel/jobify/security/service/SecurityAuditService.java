package ag.fuel.jobify.security.service;

import ag.fuel.jobify.user.entity.User;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;

/**
 * Service for auditing security-related events.
 * Logs security events for monitoring and compliance purposes.
 */
@Service
@RequiredArgsConstructor
public class SecurityAuditService {

    private static final Logger SECURITY_LOGGER = LoggerFactory.getLogger("SECURITY_AUDIT");

    /**
     * Log successful app access
     */
    public void logAppAccessGranted(User user, String appName, String userRole, HttpServletRequest request) {
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        
        SECURITY_LOGGER.info("APP_ACCESS_GRANTED | User: {} | App: {} | Role: {} | IP: {} | UserAgent: {} | Time: {}", 
            user.getEmail(), appName, userRole, clientIp, userAgent, LocalDateTime.now());
    }

    /**
     * Log denied app access
     */
    public void logAppAccessDenied(String userEmail, String appName, String reason, HttpServletRequest request) {
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        
        SECURITY_LOGGER.warn("APP_ACCESS_DENIED | User: {} | App: {} | Reason: {} | IP: {} | UserAgent: {} | Time: {}", 
            userEmail != null ? userEmail : "UNKNOWN", appName, reason, clientIp, userAgent, LocalDateTime.now());
    }

    /**
     * Log privilege escalation attempts
     */
    public void logPrivilegeEscalationAttempt(User user, String appName, String attemptedAction, String requiredRole, String userRole, HttpServletRequest request) {
        String clientIp = getClientIpAddress(request);
        
        SECURITY_LOGGER.warn("PRIVILEGE_ESCALATION_ATTEMPT | User: {} | App: {} | Action: {} | Required: {} | UserRole: {} | IP: {} | Time: {}", 
            user.getEmail(), appName, attemptedAction, requiredRole, userRole, clientIp, LocalDateTime.now());
    }

    /**
     * Log suspicious activity
     */
    public void logSuspiciousActivity(String userEmail, String activity, String details, HttpServletRequest request) {
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        
        SECURITY_LOGGER.warn("SUSPICIOUS_ACTIVITY | User: {} | Activity: {} | Details: {} | IP: {} | UserAgent: {} | Time: {}", 
            userEmail != null ? userEmail : "UNKNOWN", activity, details, clientIp, userAgent, LocalDateTime.now());
    }

    /**
     * Log security configuration changes
     */
    public void logSecurityConfigChange(User user, String changeType, String details, HttpServletRequest request) {
        String clientIp = getClientIpAddress(request);
        
        SECURITY_LOGGER.info("SECURITY_CONFIG_CHANGE | User: {} | Change: {} | Details: {} | IP: {} | Time: {}", 
            user.getEmail(), changeType, details, clientIp, LocalDateTime.now());
    }

    /**
     * Log app permission changes
     */
    public void logAppPermissionChange(User adminUser, String targetUserEmail, String appName, String action, String newRole, HttpServletRequest request) {
        String clientIp = getClientIpAddress(request);
        
        SECURITY_LOGGER.info("APP_PERMISSION_CHANGE | Admin: {} | TargetUser: {} | App: {} | Action: {} | NewRole: {} | IP: {} | Time: {}", 
            adminUser.getEmail(), targetUserEmail, appName, action, newRole, clientIp, LocalDateTime.now());
    }

    /**
     * Log failed authentication attempts
     */
    public void logFailedAuthentication(String email, String reason, HttpServletRequest request) {
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        
        SECURITY_LOGGER.warn("AUTHENTICATION_FAILED | Email: {} | Reason: {} | IP: {} | UserAgent: {} | Time: {}", 
            email, reason, clientIp, userAgent, LocalDateTime.now());
    }

    /**
     * Log successful authentication
     */
    public void logSuccessfulAuthentication(User user, HttpServletRequest request) {
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        
        SECURITY_LOGGER.info("AUTHENTICATION_SUCCESS | User: {} | IP: {} | UserAgent: {} | Time: {}", 
            user.getEmail(), clientIp, userAgent, LocalDateTime.now());
    }

    /**
     * Log session events
     */
    public void logSessionEvent(User user, String eventType, HttpServletRequest request) {
        String clientIp = getClientIpAddress(request);
        
        SECURITY_LOGGER.info("SESSION_EVENT | User: {} | Event: {} | IP: {} | Time: {}", 
            user.getEmail(), eventType, clientIp, LocalDateTime.now());
    }

    /**
     * Extract client IP address from request, handling proxies
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
