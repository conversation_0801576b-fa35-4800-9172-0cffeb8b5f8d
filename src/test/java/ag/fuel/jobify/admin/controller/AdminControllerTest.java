package ag.fuel.jobify.admin.controller;

import ag.fuel.jobify.reference.dto.VersionDto;
import ag.fuel.jobify.notification.entity.Notification;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
public class AdminControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testNotificationSerialization_ShouldNotThrowLazyInitializationException() {
        // Arrange
        Notification notification = new Notification();
        notification.setId(1L);
        notification.setTitle("Test Notification");
        notification.setSubtitle("Test Subtitle");
        notification.setType("SYSTEM");

        // Act & Assert - This should not throw a LazyInitializationException
        assertDoesNotThrow(() -> {
            String json = objectMapper.writeValueAsString(notification);
            System.out.println("[DEBUG_LOG] Successfully serialized Notification object: " + json);
        });
    }

    @Test
    public void testAddVersion_ShouldNotThrowLazyInitializationException() throws Exception {
        // Arrange
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        VersionDto versionDto = new VersionDto("1.0.0", false);

        // Act & Assert - This should not throw a LazyInitializationException
        mockMvc.perform(post("/admin/addVersion")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(versionDto)))
                .andExpect(status().isOk());
    }
}