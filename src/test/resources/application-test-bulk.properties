# Bulk Test Configuration
# Copy these properties to your application-test.properties or create a separate profile

# =============================================================================
# BULK NOTIFICATION TEST CONFIGURATION
# =============================================================================

# Number of notifications to create (default: 1000)
test.bulk.notification-count=10

# Batch size for progress logging (default: 100)
test.bulk.batch-size=10

# Whether to log progress during bulk operations (default: true)
test.bulk.log-progress=true

# Whether to create test users if none exist (default: true)
test.bulk.create-test-users=true

# Number of test users to create if needed (default: 5)
test.bulk.test-user-count=5

# =============================================================================
# TRANSACTIONAL vs DEFINITIVE MODE
# =============================================================================

# Whether the bulk insert should be transactional (rollback after test) or definitive (permanent)
# true = TRANSACTIONAL (safe for testing, records will be rolled back)
# false = DEFINITIVE (permanent records, use with caution!)
test.bulk.transactional=true

# Email of the real user to assign notifications to when creating definitive records
# Only used when transactional=false and system-notifications=false
# If null or empty, will use SYSTEM notifications for all users
test.bulk.real-user-email=<EMAIL>

# Whether to create SYSTEM notifications (for all users) or USER notifications (for specific user)
# true = SYSTEM notifications (sent to all users)
# false = USER notifications (sent only to the user specified in real-user-email)
test.bulk.system-notifications=true

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# Example 1: Safe testing with 100 transactional notifications
# test.bulk.notification-count=100
# test.bulk.transactional=true
# test.bulk.system-notifications=true

# Example 2: Create 50 permanent SYSTEM notifications for all users
# test.bulk.notification-count=50
# test.bulk.transactional=false
# test.bulk.system-notifications=true

# Example 3: Create 25 permanent USER notifications for a specific user
# test.bulk.notification-count=25
# test.bulk.transactional=false
# test.bulk.system-notifications=false
# test.bulk.real-user-email=<EMAIL>

# Example 4: Large scale testing with 5000 transactional notifications
# test.bulk.notification-count=5000
# test.bulk.batch-size=250
# test.bulk.transactional=true
# test.bulk.log-progress=true
