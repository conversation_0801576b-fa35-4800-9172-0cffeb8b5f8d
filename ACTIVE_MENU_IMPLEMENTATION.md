# Active Menu State Implementation Guide

This document explains the implementation of active menu state highlighting for the dynamic app menu system.

## 🎯 Overview

The active menu state system provides visual feedback to users about their current location within the application, enhancing navigation and user experience.

## ✨ Features Implemented

### 1. **Dynamic App Detection**
- **URL Pattern Matching**: Automatically detects current app from URL patterns like `/app/{appName}`
- **Global Context**: Available across all templates through `GlobalControllerAllPages`
- **Flexible Routing**: Supports nested routes like `/app/purchasing/settings`

### 2. **Visual Highlighting**
- **Active Menu Items**: Highlighted with primary color and background
- **Active Indicators**: Animated badge with pulse effect
- **Smooth Transitions**: CSS transitions for better user experience
- **Accessibility**: Enhanced focus states for keyboard navigation

### 3. **Breadcrumb Navigation**
- **Contextual Breadcrumbs**: Shows current app and page hierarchy
- **App Headers**: Unified header with app icon, name, and description
- **Quick Navigation**: Role-based navigation buttons for different app sections

## 🔧 Technical Implementation

### Backend Components

#### 1. GlobalControllerAllPages Enhancement
**Location**: `src/main/java/ag/fuel/jobify/common/controller/GlobalControllerAllPages.java`

**Key Method**:
```java
private String extractAppNameFromUrl(String requestUri) {
    Pattern appPattern = Pattern.compile("^/app/([a-zA-Z]+)(?:/.*)?$");
    Matcher matcher = appPattern.matcher(requestUri);
    return matcher.matches() ? matcher.group(1).toLowerCase() : null;
}
```

**Model Attributes Added**:
- `currentAppName`: Detected app name for active highlighting
- `breadcrumbPage`: Current page context for breadcrumbs

#### 2. AppController Updates
**Enhanced Methods**:
- `appDashboard()`: Sets `breadcrumbPage = "Dashboard"`
- `appSettings()`: Sets `breadcrumbPage = "Settings"`
- `appAdmin()`: Sets `breadcrumbPage = "Admin"`

### Frontend Components

#### 1. Menu Template Enhancement
**Location**: `src/main/resources/templates/fragments/menu.html`

**Active State Logic**:
```html
<li th:class="${'menu-item' + (currentAppName != null and currentAppName == app.appName.toLowerCase() ? ' active' : '')}">
    <a th:class="${'menu-link' + (currentAppName != null and currentAppName == app.appName.toLowerCase() ? ' active' : '')}">
        <!-- App content -->
        <span th:if="${currentAppName != null and currentAppName == app.appName.toLowerCase()}" 
              class="badge badge-center rounded-pill bg-primary ms-auto">
            <i class="ti ti-point ti-xs"></i>
        </span>
    </a>
</li>
```

#### 2. CSS Styling
**Location**: `src/main/resources/static/css/live-overrides.css`

**Key Styles**:
```css
/* Active menu item styling */
.menu-item.active {
    background-color: rgba(var(--bs-primary-rgb), 0.08);
    border-radius: 0.375rem;
    margin: 0.125rem 0.75rem;
}

/* Active menu link styling */
.menu-link.active {
    color: rgb(var(--bs-primary-rgb)) !important;
    background-color: rgba(var(--bs-primary-rgb), 0.08);
    font-weight: 600;
}

/* Pulse animation for active indicator */
@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}
```

#### 3. Breadcrumb Component
**Location**: `src/main/resources/templates/fragments/breadcrumb.html`

**Components**:
- `breadcrumb`: Basic breadcrumb navigation
- `app-header`: App header with title, description, and breadcrumb
- `app-navigation`: Quick navigation buttons for app sections

## 🎨 Visual Design

### Active Menu States

| State | Visual Indicators |
|-------|------------------|
| **Active App** | Primary color background, bold text, animated badge |
| **Hover** | Slightly darker background, smooth transition |
| **Focus** | Outline for keyboard navigation |
| **Normal** | Default styling |

### Color Scheme
- **Primary Color**: Bootstrap primary (customizable via CSS variables)
- **Background**: Semi-transparent primary color (8% opacity)
- **Hover**: Slightly more opaque (12% opacity)
- **Text**: Primary color for active items

### Animations
- **Pulse Effect**: Active indicator badge pulses every 2 seconds
- **Smooth Transitions**: 0.2s ease-in-out for all state changes
- **Scale Animation**: Badge scales from 1.0 to 1.1 during pulse

## 🧪 Testing Scenarios

### 1. **Basic Navigation**
```bash
# Test active state highlighting
1. Login as any user with app access
2. Navigate to /app/purchasing
3. Verify "Purchasing" menu item is highlighted
4. Navigate to /app/finance
5. Verify "Finance" menu item is now highlighted
```

### 2. **Nested Routes**
```bash
# Test active state on nested routes
1. Navigate to /app/purchasing/settings
2. Verify "Purchasing" menu item remains highlighted
3. Check breadcrumb shows: Dashboard > Purchasing > Settings
```

### 3. **Role-Based Navigation**
```bash
# Test navigation buttons based on user role
1. <NAME_EMAIL> (ADMIN)
2. Navigate to any app
3. Verify Dashboard, Settings, and Admin buttons are visible
4. <NAME_EMAIL> (USER)
5. Verify only Dashboard button is visible
```

### 4. **Cross-App Navigation**
```bash
# Test active state changes between apps
1. Start at /app/purchasing (highlighted)
2. Click Finance app in menu
3. Verify Purchasing is no longer highlighted
4. Verify Finance is now highlighted
```

## 📱 Responsive Design

### Mobile Considerations
- **Touch-Friendly**: Adequate spacing for touch targets
- **Readable Text**: Maintains readability on small screens
- **Accessible Colors**: High contrast for visibility

### Breakpoint Behavior
- **Desktop**: Full menu with all visual indicators
- **Tablet**: Condensed menu with maintained highlighting
- **Mobile**: Collapsible menu with active state preserved

## ♿ Accessibility Features

### Keyboard Navigation
- **Tab Order**: Logical tab sequence through menu items
- **Focus Indicators**: Clear visual focus states
- **Screen Reader**: Proper ARIA labels and roles

### Visual Accessibility
- **High Contrast**: Sufficient color contrast ratios
- **Color Independence**: Not relying solely on color for indication
- **Text Alternatives**: Icons paired with text labels

## 🔧 Customization Options

### CSS Variables
```css
:root {
    --bs-primary-rgb: 13, 110, 253; /* Customize primary color */
    --menu-active-opacity: 0.08;    /* Customize background opacity */
    --menu-transition-duration: 0.2s; /* Customize transition speed */
}
```

### Template Customization
- **Icons**: Change app icons in menu template
- **Badge Style**: Modify active indicator appearance
- **Layout**: Adjust breadcrumb and header layout

## 📈 Performance Considerations

### Optimization Features
- **CSS-Only Animations**: No JavaScript required for visual effects
- **Minimal DOM Changes**: Only classes are toggled
- **Efficient Selectors**: Optimized CSS selectors for performance

### Browser Support
- **Modern Browsers**: Full feature support
- **Legacy Browsers**: Graceful degradation
- **CSS Variables**: Fallback values provided

## 🚀 Future Enhancements

### Potential Improvements
1. **Persistent State**: Remember last visited app section
2. **Keyboard Shortcuts**: Quick navigation between apps
3. **Customizable Themes**: User-selectable color schemes
4. **Advanced Animations**: More sophisticated transition effects

The active menu state system provides a polished, professional navigation experience that clearly communicates the user's current location within the application! 🎯
